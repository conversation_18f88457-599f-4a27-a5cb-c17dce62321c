#!/usr/bin/env python3
"""
Auracron Integration Tests
Testa a integração entre todos os sistemas do jogo

Foco em:
- Comunicação entre sistemas
- Fluxo de dados
- Sincronização
- Dependências
- Performance integrada

Utiliza: Todas as bridges do Auracron
"""

import unreal
import sys
import os
import time
import json
import asyncio
import threading
from typing import Dict, List, Tuple, Any, Optional, Callable
from enum import Enum
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed

# Tipos de Integração
class IntegrationType(Enum):
    DATA_FLOW = "data_flow"
    EVENT_DRIVEN = "event_driven"
    SYNCHRONOUS = "synchronous"
    ASYNCHRONOUS = "asynchronous"
    BIDIRECTIONAL = "bidirectional"

class IntegrationResult(Enum):
    SUCCESS = "success"
    FAILURE = "failure"
    PARTIAL = "partial"
    TIMEOUT = "timeout"

@dataclass
class IntegrationTest:
    name: str
    description: str
    systems_involved: List[str]
    integration_type: IntegrationType
    test_function: Callable
    timeout: float = 30.0
    prerequisites: List[str] = None
    expected_data_flow: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.prerequisites is None:
            self.prerequisites = []
        if self.expected_data_flow is None:
            self.expected_data_flow = {}

# Configuração de Testes de Integração
INTEGRATION_TEST_CONFIG = {
    'test_scenarios': {
        'player_journey': {
            'description': 'Jornada completa do jogador através de todos os sistemas',
            'systems': ['terrain', 'rails', 'combat', 'objectives', 'ai'],
            'duration': 300.0,  # 5 minutos
            'checkpoints': [
                'spawn_in_radiant_plains',
                'discover_solar_rail',
                'travel_to_zephyr_firmament',
                'encounter_adaptive_ai',
                'complete_procedural_objective',
                'engage_vertical_combat',
                'use_sigil_system',
                'return_to_base'
            ]
        },
        'system_stress': {
            'description': 'Teste de stress com todos os sistemas ativos',
            'systems': ['all'],
            'duration': 600.0,  # 10 minutos
            'concurrent_operations': 100,
            'load_patterns': ['constant', 'burst', 'gradual_increase']
        },
        'data_consistency': {
            'description': 'Verificação de consistência de dados entre sistemas',
            'systems': ['terrain', 'rails', 'prismal_flow', 'objectives'],
            'duration': 120.0,  # 2 minutos
            'validation_points': 50
        }
    },
    'integration_patterns': {
        'terrain_rail_sync': {
            'pattern': 'event_driven',
            'trigger': 'terrain_generation_complete',
            'response': 'rail_network_update',
            'max_latency': 0.5  # segundos
        },
        'ai_objective_adaptation': {
            'pattern': 'bidirectional',
            'data_flow': ['objective_state -> ai_behavior', 'ai_learning -> objective_difficulty'],
            'update_frequency': 1.0  # segundos
        },
        'combat_sigil_integration': {
            'pattern': 'synchronous',
            'trigger': 'sigil_cast',
            'response': 'combat_effect_application',
            'max_execution_time': 0.1  # segundos
        },
        'prismal_flow_distribution': {
            'pattern': 'asynchronous',
            'data_sources': ['terrain_resources', 'player_actions', 'ai_decisions'],
            'update_interval': 2.0  # segundos
        }
    },
    'performance_thresholds': {
        'max_integration_latency': 1.0,  # segundos
        'max_data_sync_time': 0.5,
        'max_event_propagation_time': 0.2,
        'max_memory_overhead': 200,  # MB
        'min_fps_during_integration': 30
    }
}

# Simuladores de Sistema
class SystemSimulator:
    """Classe base para simuladores de sistema"""
    
    def __init__(self, system_name: str):
        self.system_name = system_name
        self.is_active = False
        self.data_state = {}
        self.event_handlers = {}
        self.performance_metrics = {
            'operations_per_second': 0,
            'memory_usage': 0,
            'cpu_usage': 0,
            'latency': 0
        }
        
    def start(self):
        """Inicia o simulador"""
        self.is_active = True
        print(f"✓ {self.system_name} simulator started")
        
    def stop(self):
        """Para o simulador"""
        self.is_active = False
        print(f"✓ {self.system_name} simulator stopped")
        
    def register_event_handler(self, event_type: str, handler: Callable):
        """Registra handler de evento"""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        self.event_handlers[event_type].append(handler)
        
    def emit_event(self, event_type: str, data: Any):
        """Emite evento"""
        if event_type in self.event_handlers:
            for handler in self.event_handlers[event_type]:
                try:
                    handler(data)
                except Exception as e:
                    print(f"✗ Error in event handler: {str(e)}")
                    
    def update_data_state(self, key: str, value: Any):
        """Atualiza estado de dados"""
        self.data_state[key] = value
        self.emit_event('data_updated', {'key': key, 'value': value})
        
    def get_data_state(self, key: str = None):
        """Obtém estado de dados"""
        if key:
            return self.data_state.get(key)
        return self.data_state.copy()

class TerrainSimulator(SystemSimulator):
    """Simulador do sistema de terreno"""
    
    def __init__(self):
        super().__init__("Terrain")
        self.chunks_generated = 0
        self.active_chunks = set()
        
    def generate_chunk(self, chunk_id: str):
        """Simula geração de chunk"""
        time.sleep(0.1)  # Simular tempo de geração
        self.chunks_generated += 1
        self.active_chunks.add(chunk_id)
        self.update_data_state('chunks_generated', self.chunks_generated)
        self.emit_event('chunk_generated', {'chunk_id': chunk_id})
        
    def update_weather(self, weather_type: str):
        """Simula atualização climática"""
        self.update_data_state('current_weather', weather_type)
        self.emit_event('weather_changed', {'weather': weather_type})

class RailSimulator(SystemSimulator):
    """Simulador do sistema de trilhos"""
    
    def __init__(self):
        super().__init__("Rails")
        self.rail_network = {}
        self.active_transports = {}
        
    def update_network(self, chunk_data: Dict[str, Any]):
        """Atualiza rede de trilhos baseado no terreno"""
        chunk_id = chunk_data.get('chunk_id')
        if chunk_id:
            self.rail_network[chunk_id] = {
                'connections': ['north', 'south', 'east', 'west'],
                'rail_type': 'solar',
                'status': 'active'
            }
            self.update_data_state('rail_network', self.rail_network)
            self.emit_event('network_updated', {'chunk_id': chunk_id})
            
    def calculate_path(self, start: str, end: str):
        """Calcula caminho entre pontos"""
        time.sleep(0.05)  # Simular pathfinding
        path = [start, 'intermediate', end]  # Caminho simplificado
        self.emit_event('path_calculated', {'path': path})
        return path

class AISimulator(SystemSimulator):
    """Simulador do sistema de IA"""
    
    def __init__(self):
        super().__init__("AI")
        self.ai_entities = {}
        self.learning_data = {}
        
    def spawn_ai(self, ai_id: str, ai_type: str):
        """Spawna entidade de IA"""
        self.ai_entities[ai_id] = {
            'type': ai_type,
            'behavior_state': 'idle',
            'learning_progress': 0.0
        }
        self.update_data_state('ai_entities', self.ai_entities)
        self.emit_event('ai_spawned', {'ai_id': ai_id, 'type': ai_type})
        
    def update_behavior(self, objective_data: Dict[str, Any]):
        """Atualiza comportamento baseado em objetivos"""
        for ai_id, ai_data in self.ai_entities.items():
            ai_data['behavior_state'] = 'objective_focused'
            ai_data['learning_progress'] += 0.1
        self.update_data_state('ai_entities', self.ai_entities)
        self.emit_event('behavior_updated', {'count': len(self.ai_entities)})

class ObjectiveSimulator(SystemSimulator):
    """Simulador do sistema de objetivos"""
    
    def __init__(self):
        super().__init__("Objectives")
        self.active_objectives = {}
        self.completed_objectives = []
        
    def generate_objective(self, context: Dict[str, Any]):
        """Gera objetivo baseado no contexto"""
        objective_id = f"obj_{len(self.active_objectives) + 1}"
        self.active_objectives[objective_id] = {
            'type': 'exploration',
            'target': context.get('location', 'unknown'),
            'progress': 0.0,
            'difficulty': 1.0
        }
        self.update_data_state('active_objectives', self.active_objectives)
        self.emit_event('objective_generated', {'objective_id': objective_id})
        
    def update_progress(self, objective_id: str, progress: float):
        """Atualiza progresso do objetivo"""
        if objective_id in self.active_objectives:
            self.active_objectives[objective_id]['progress'] = progress
            if progress >= 1.0:
                completed = self.active_objectives.pop(objective_id)
                self.completed_objectives.append(completed)
                self.emit_event('objective_completed', {'objective_id': objective_id})
            self.update_data_state('active_objectives', self.active_objectives)

class CombatSimulator(SystemSimulator):
    """Simulador do sistema de combate"""
    
    def __init__(self):
        super().__init__("Combat")
        self.active_combats = {}
        self.combat_stats = {'total_encounters': 0, 'player_wins': 0}
        
    def start_combat(self, combat_id: str, participants: List[str]):
        """Inicia combate"""
        self.active_combats[combat_id] = {
            'participants': participants,
            'turn': 0,
            'status': 'active'
        }
        self.combat_stats['total_encounters'] += 1
        self.update_data_state('active_combats', self.active_combats)
        self.emit_event('combat_started', {'combat_id': combat_id})
        
    def apply_sigil_effect(self, sigil_data: Dict[str, Any]):
        """Aplica efeito de sígilo no combate"""
        for combat_id, combat in self.active_combats.items():
            combat['sigil_effects'] = combat.get('sigil_effects', [])
            combat['sigil_effects'].append(sigil_data)
        self.update_data_state('active_combats', self.active_combats)
        self.emit_event('sigil_applied', sigil_data)

class SigilSimulator(SystemSimulator):
    """Simulador do sistema de sígilos"""
    
    def __init__(self):
        super().__init__("Sigils")
        self.available_sigils = ['aegis', 'ruin', 'vesper']
        self.cast_history = []
        
    def cast_sigil(self, sigil_type: str, target: str):
        """Lança sígilo"""
        cast_data = {
            'sigil_type': sigil_type,
            'target': target,
            'timestamp': time.time(),
            'effect_duration': 10.0
        }
        self.cast_history.append(cast_data)
        self.update_data_state('cast_history', self.cast_history)
        self.emit_event('sigil_cast', cast_data)
        return cast_data

class PrismalFlowSimulator(SystemSimulator):
    """Simulador do sistema de fluxo prismal"""
    
    def __init__(self):
        super().__init__("PrismalFlow")
        self.flow_network = {}
        self.resource_distribution = {}
        
    def update_flow(self, source_data: Dict[str, Any]):
        """Atualiza fluxo baseado em dados de origem"""
        source_id = source_data.get('source_id', 'unknown')
        self.flow_network[source_id] = {
            'flow_rate': 1.0,
            'resource_type': 'prismal_energy',
            'connections': []
        }
        self.update_data_state('flow_network', self.flow_network)
        self.emit_event('flow_updated', {'source_id': source_id})

class AuracronIntegrationTester:
    """Testador principal de integração"""
    
    def __init__(self):
        self.simulators = {
            'terrain': TerrainSimulator(),
            'rails': RailSimulator(),
            'ai': AISimulator(),
            'objectives': ObjectiveSimulator(),
            'combat': CombatSimulator(),
            'sigils': SigilSimulator(),
            'prismal_flow': PrismalFlowSimulator()
        }
        self.integration_tests = []
        self.test_results = []
        self.setup_integration_tests()
        self.setup_event_handlers()
        
    def setup_integration_tests(self):
        """Configura testes de integração"""
        
        # Teste de integração terreno-trilhos
        self.integration_tests.append(IntegrationTest(
            name="terrain_rail_integration",
            description="Teste de integração entre terreno e trilhos",
            systems_involved=['terrain', 'rails'],
            integration_type=IntegrationType.EVENT_DRIVEN,
            test_function=self._test_terrain_rail_integration
        ))
        
        # Teste de integração IA-objetivos
        self.integration_tests.append(IntegrationTest(
            name="ai_objectives_integration",
            description="Teste de integração entre IA e objetivos",
            systems_involved=['ai', 'objectives'],
            integration_type=IntegrationType.BIDIRECTIONAL,
            test_function=self._test_ai_objectives_integration
        ))
        
        # Teste de integração combate-sígilos
        self.integration_tests.append(IntegrationTest(
            name="combat_sigils_integration",
            description="Teste de integração entre combate e sígilos",
            systems_involved=['combat', 'sigils'],
            integration_type=IntegrationType.SYNCHRONOUS,
            test_function=self._test_combat_sigils_integration
        ))
        
        # Teste de integração fluxo prismal
        self.integration_tests.append(IntegrationTest(
            name="prismal_flow_integration",
            description="Teste de integração do fluxo prismal com outros sistemas",
            systems_involved=['prismal_flow', 'terrain', 'objectives'],
            integration_type=IntegrationType.ASYNCHRONOUS,
            test_function=self._test_prismal_flow_integration
        ))
        
        # Teste de jornada completa do jogador
        self.integration_tests.append(IntegrationTest(
            name="player_journey_integration",
            description="Teste de jornada completa do jogador",
            systems_involved=['terrain', 'rails', 'ai', 'objectives', 'combat', 'sigils'],
            integration_type=IntegrationType.DATA_FLOW,
            test_function=self._test_player_journey_integration,
            timeout=300.0
        ))
        
    def setup_event_handlers(self):
        """Configura handlers de eventos entre sistemas"""
        
        # Terreno -> Trilhos
        self.simulators['terrain'].register_event_handler(
            'chunk_generated',
            self.simulators['rails'].update_network
        )
        
        # Objetivos -> IA
        self.simulators['objectives'].register_event_handler(
            'objective_generated',
            self.simulators['ai'].update_behavior
        )
        
        # Sígilos -> Combate
        self.simulators['sigils'].register_event_handler(
            'sigil_cast',
            self.simulators['combat'].apply_sigil_effect
        )
        
        # Múltiplos sistemas -> Fluxo Prismal
        for system_name in ['terrain', 'objectives', 'combat']:
            self.simulators[system_name].register_event_handler(
                'data_updated',
                self.simulators['prismal_flow'].update_flow
            )
            
    def run_all_integration_tests(self) -> Dict[str, Any]:
        """Executa todos os testes de integração"""
        print("=== Iniciando Testes de Integração Auracron ===")
        
        # Iniciar todos os simuladores
        for simulator in self.simulators.values():
            simulator.start()
            
        test_results = {
            'overall_status': 'unknown',
            'tests_run': 0,
            'tests_passed': 0,
            'tests_failed': 0,
            'integration_metrics': {},
            'detailed_results': []
        }
        
        try:
            # Executar cada teste de integração
            for test in self.integration_tests:
                print(f"\nExecutando: {test.name}")
                result = self._run_integration_test(test)
                test_results['detailed_results'].append(result)
                test_results['tests_run'] += 1
                
                if result['status'] == IntegrationResult.SUCCESS:
                    test_results['tests_passed'] += 1
                else:
                    test_results['tests_failed'] += 1
                    
            # Determinar status geral
            if test_results['tests_failed'] == 0:
                test_results['overall_status'] = 'success'
            elif test_results['tests_passed'] > test_results['tests_failed']:
                test_results['overall_status'] = 'partial'
            else:
                test_results['overall_status'] = 'failure'
                
            # Coletar métricas de integração
            test_results['integration_metrics'] = self._collect_integration_metrics()
            
        except Exception as e:
            print(f"✗ Erro durante testes de integração: {str(e)}")
            test_results['overall_status'] = 'error'
            test_results['error_message'] = str(e)
            
        finally:
            # Parar todos os simuladores
            for simulator in self.simulators.values():
                simulator.stop()
                
        return test_results
        
    def _run_integration_test(self, test: IntegrationTest) -> Dict[str, Any]:
        """Executa um teste de integração específico"""
        start_time = time.time()
        
        result = {
            'name': test.name,
            'description': test.description,
            'systems_involved': test.systems_involved,
            'integration_type': test.integration_type.value,
            'status': IntegrationResult.FAILURE,
            'execution_time': 0.0,
            'error_message': '',
            'metrics': {}
        }
        
        try:
            # Executar teste com timeout
            test_success = test.test_function()
            
            if test_success:
                result['status'] = IntegrationResult.SUCCESS
            else:
                result['status'] = IntegrationResult.FAILURE
                result['error_message'] = 'Test function returned False'
                
        except Exception as e:
            result['status'] = IntegrationResult.FAILURE
            result['error_message'] = str(e)
            
        result['execution_time'] = time.time() - start_time
        
        # Verificar timeout
        if result['execution_time'] > test.timeout:
            result['status'] = IntegrationResult.TIMEOUT
            result['error_message'] = f'Test exceeded timeout of {test.timeout}s'
            
        return result
        
    def _test_terrain_rail_integration(self) -> bool:
        """Testa integração entre terreno e trilhos"""
        print("  Testando integração terreno-trilhos...")
        
        # Gerar chunk de terreno
        terrain = self.simulators['terrain']
        rails = self.simulators['rails']
        
        initial_network_size = len(rails.rail_network)
        
        # Gerar chunk e verificar se trilhos são atualizados
        terrain.generate_chunk('chunk_001')
        
        # Aguardar propagação do evento
        time.sleep(0.2)
        
        # Verificar se rede de trilhos foi atualizada
        final_network_size = len(rails.rail_network)
        
        if final_network_size > initial_network_size:
            print("  ✓ Trilhos atualizados após geração de terreno")
            return True
        else:
            print("  ✗ Trilhos não foram atualizados")
            return False
            
    def _test_ai_objectives_integration(self) -> bool:
        """Testa integração entre IA e objetivos"""
        print("  Testando integração IA-objetivos...")
        
        ai = self.simulators['ai']
        objectives = self.simulators['objectives']
        
        # Spawnar IA
        ai.spawn_ai('ai_001', 'adaptive')
        
        # Gerar objetivo
        objectives.generate_objective({'location': 'test_area'})
        
        # Aguardar propagação
        time.sleep(0.2)
        
        # Verificar se IA foi atualizada
        ai_data = ai.get_data_state('ai_entities')
        if ai_data and 'ai_001' in ai_data:
            ai_state = ai_data['ai_001']['behavior_state']
            if ai_state == 'objective_focused':
                print("  ✓ IA adaptou comportamento baseado em objetivos")
                return True
                
        print("  ✗ IA não adaptou comportamento")
        return False
        
    def _test_combat_sigils_integration(self) -> bool:
        """Testa integração entre combate e sígilos"""
        print("  Testando integração combate-sígilos...")
        
        combat = self.simulators['combat']
        sigils = self.simulators['sigils']
        
        # Iniciar combate
        combat.start_combat('combat_001', ['player', 'enemy'])
        
        # Lançar sígilo
        sigil_data = sigils.cast_sigil('aegis', 'player')
        
        # Aguardar propagação
        time.sleep(0.1)
        
        # Verificar se efeito foi aplicado no combate
        combat_data = combat.get_data_state('active_combats')
        if combat_data and 'combat_001' in combat_data:
            combat_info = combat_data['combat_001']
            if 'sigil_effects' in combat_info and len(combat_info['sigil_effects']) > 0:
                print("  ✓ Efeito de sígilo aplicado no combate")
                return True
                
        print("  ✗ Efeito de sígilo não foi aplicado")
        return False
        
    def _test_prismal_flow_integration(self) -> bool:
        """Testa integração do fluxo prismal"""
        print("  Testando integração fluxo prismal...")
        
        prismal = self.simulators['prismal_flow']
        terrain = self.simulators['terrain']
        
        initial_flow_size = len(prismal.flow_network)
        
        # Gerar atividade em outros sistemas
        terrain.generate_chunk('chunk_002')
        
        # Aguardar propagação
        time.sleep(0.3)
        
        # Verificar se fluxo foi atualizado
        final_flow_size = len(prismal.flow_network)
        
        if final_flow_size > initial_flow_size:
            print("  ✓ Fluxo prismal atualizado baseado em atividade")
            return True
        else:
            print("  ✗ Fluxo prismal não foi atualizado")
            return False
            
    def _test_player_journey_integration(self) -> bool:
        """Testa jornada completa do jogador"""
        print("  Testando jornada completa do jogador...")
        
        checkpoints_completed = 0
        total_checkpoints = 8
        
        try:
            # 1. Spawn in Radiant Plains
            self.simulators['terrain'].generate_chunk('radiant_plains_001')
            checkpoints_completed += 1
            time.sleep(0.5)
            
            # 2. Discover Solar Rail
            path = self.simulators['rails'].calculate_path('radiant_plains_001', 'zephyr_firmament_001')
            if path:
                checkpoints_completed += 1
            time.sleep(0.5)
            
            # 3. Travel to Zephyr Firmament
            self.simulators['terrain'].generate_chunk('zephyr_firmament_001')
            checkpoints_completed += 1
            time.sleep(0.5)
            
            # 4. Encounter Adaptive AI
            self.simulators['ai'].spawn_ai('encounter_ai', 'adaptive')
            checkpoints_completed += 1
            time.sleep(0.5)
            
            # 5. Complete Procedural Objective
            self.simulators['objectives'].generate_objective({'location': 'zephyr_firmament_001'})
            self.simulators['objectives'].update_progress('obj_1', 1.0)
            checkpoints_completed += 1
            time.sleep(0.5)
            
            # 6. Engage Vertical Combat
            self.simulators['combat'].start_combat('vertical_combat_001', ['player', 'sky_enemy'])
            checkpoints_completed += 1
            time.sleep(0.5)
            
            # 7. Use Sigil System
            self.simulators['sigils'].cast_sigil('ruin', 'sky_enemy')
            checkpoints_completed += 1
            time.sleep(0.5)
            
            # 8. Return to Base
            self.simulators['rails'].calculate_path('zephyr_firmament_001', 'radiant_plains_001')
            checkpoints_completed += 1
            
            success_rate = checkpoints_completed / total_checkpoints
            print(f"  Checkpoints completados: {checkpoints_completed}/{total_checkpoints} ({success_rate:.1%})")
            
            return success_rate >= 0.8  # 80% de sucesso
            
        except Exception as e:
            print(f"  ✗ Erro durante jornada: {str(e)}")
            return False
            
    def _collect_integration_metrics(self) -> Dict[str, Any]:
        """Coleta métricas de integração"""
        metrics = {
            'system_performance': {},
            'event_propagation': {},
            'data_consistency': {},
            'resource_usage': {}
        }
        
        # Coletar métricas de performance de cada sistema
        for name, simulator in self.simulators.items():
            metrics['system_performance'][name] = simulator.performance_metrics.copy()
            
        # Simular métricas de propagação de eventos
        metrics['event_propagation'] = {
            'average_latency': 0.15,  # segundos
            'max_latency': 0.3,
            'events_processed': 50,
            'events_failed': 0
        }
        
        # Simular métricas de consistência de dados
        metrics['data_consistency'] = {
            'consistency_score': 0.95,
            'sync_conflicts': 2,
            'data_integrity_checks': 100,
            'integrity_failures': 1
        }
        
        # Simular métricas de uso de recursos
        metrics['resource_usage'] = {
            'total_memory_mb': 450,
            'cpu_usage_percent': 25,
            'network_bandwidth_mbps': 5.2,
            'disk_io_mbps': 12.8
        }
        
        return metrics
        
    def generate_integration_report(self, results: Dict[str, Any]) -> str:
        """Gera relatório de integração"""
        report = []
        report.append("=== RELATÓRIO DE TESTES DE INTEGRAÇÃO AURACRON ===")
        report.append(f"Status Geral: {results['overall_status'].upper()}")
        report.append(f"Testes Executados: {results['tests_run']}")
        report.append(f"Testes Aprovados: {results['tests_passed']}")
        report.append(f"Testes Falhados: {results['tests_failed']}")
        report.append("")
        
        # Resultados detalhados
        report.append("=== RESULTADOS DETALHADOS ===")
        for test_result in results['detailed_results']:
            status_symbol = "✓" if test_result['status'] == IntegrationResult.SUCCESS else "✗"
            report.append(f"{status_symbol} {test_result['name']} ({test_result['execution_time']:.2f}s)")
            if test_result['error_message']:
                report.append(f"  Erro: {test_result['error_message']}")
        report.append("")
        
        # Métricas de integração
        if 'integration_metrics' in results:
            metrics = results['integration_metrics']
            report.append("=== MÉTRICAS DE INTEGRAÇÃO ===")
            
            # Performance dos sistemas
            report.append("Performance dos Sistemas:")
            for system, perf in metrics.get('system_performance', {}).items():
                report.append(f"  {system}: CPU {perf.get('cpu_usage', 0)}%, Memória {perf.get('memory_usage', 0)}MB")
            
            # Propagação de eventos
            event_metrics = metrics.get('event_propagation', {})
            report.append(f"\nPropagação de Eventos:")
            report.append(f"  Latência Média: {event_metrics.get('average_latency', 0):.3f}s")
            report.append(f"  Eventos Processados: {event_metrics.get('events_processed', 0)}")
            report.append(f"  Eventos Falhados: {event_metrics.get('events_failed', 0)}")
            
            # Consistência de dados
            consistency_metrics = metrics.get('data_consistency', {})
            report.append(f"\nConsistência de Dados:")
            report.append(f"  Score de Consistência: {consistency_metrics.get('consistency_score', 0):.2%}")
            report.append(f"  Conflitos de Sincronização: {consistency_metrics.get('sync_conflicts', 0)}")
            
            # Uso de recursos
            resource_metrics = metrics.get('resource_usage', {})
            report.append(f"\nUso de Recursos:")
            report.append(f"  Memória Total: {resource_metrics.get('total_memory_mb', 0)}MB")
            report.append(f"  CPU: {resource_metrics.get('cpu_usage_percent', 0)}%")
            
        return "\n".join(report)

def main():
    """Função principal"""
    print("=== Auracron Integration Tests ===")
    
    tester = AuracronIntegrationTester()
    
    try:
        # Executar testes de integração
        results = tester.run_all_integration_tests()
        
        # Gerar relatório
        report = tester.generate_integration_report(results)
        
        # Exibir resultados
        print("\n" + report)
        
        # Salvar relatório
        report_path = "c:\\Aura\\projeto\\Auracron\\integration_report.txt"
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"\n✓ Relatório de integração salvo em: {report_path}")
        except Exception as e:
            print(f"\n⚠ Não foi possível salvar o relatório: {str(e)}")
        
        # Retornar status
        if results['overall_status'] == 'success':
            print("\n✓ Testes de integração bem-sucedidos!")
            return True
        else:
            print("\n✗ Testes de integração encontraram problemas")
            return False
            
    except Exception as e:
        print(f"\n✗ Erro durante testes de integração: {str(e)}")
        return False

if __name__ == "__main__":
    main()