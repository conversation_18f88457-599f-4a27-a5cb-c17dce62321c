/**
 * AuracronPrismalFlow.cpp
 * 
 * Implementation of the central Prismal Flow system - the serpentine energy river
 * that connects all three realm layers with strategic islands.
 * 
 * Uses UE 5.6 modern APIs for spline-based flow generation, procedural content,
 * and dynamic visual effects.
 */

#include "AuracronPrismalFlow.h"
#include "AuracronDynamicRealmSubsystem.h"
#include "AuracronPrismalIsland.h"
#include "Components/SplineComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/AudioComponent.h"
#include "Components/SphereComponent.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetMathLibrary.h"
#include "PCGComponent.h"
#include "PCGGraph.h"
#include "GameplayTagContainer.h"
#include "AbilitySystemComponent.h"
#include "GameplayEffect.h"
#include "Sound/SoundBase.h"
#include "MetasoundSource.h"
#include "Engine/Engine.h"

AAuracronPrismalFlow::AAuracronPrismalFlow()
{
    PrimaryActorTick.bCanEverTick = true;
    PrimaryActorTick.bStartWithTickEnabled = true;
    PrimaryActorTick.TickInterval = 0.1f; // 10 FPS for flow updates

    // Create root component
    RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootComponent"));

    // Create spline component for flow path using UE 5.6 spline system
    FlowSpline = CreateDefaultSubobject<USplineComponent>(TEXT("FlowSpline"));
    FlowSpline->SetupAttachment(RootComponent);
    FlowSpline->SetClosedLoop(false);
    FlowSpline->SetSplinePointType(0, ESplinePointType::CurveClamped);

    // Create mesh component for flow visualization
    FlowMeshComponent = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("FlowMeshComponent"));
    FlowMeshComponent->SetupAttachment(RootComponent);
    FlowMeshComponent->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
    FlowMeshComponent->SetCollisionObjectType(ECollisionChannel::ECC_WorldStatic);
    FlowMeshComponent->SetCollisionResponseToAllChannels(ECollisionResponse::ECR_Ignore);
    FlowMeshComponent->SetCollisionResponseToChannel(ECollisionChannel::ECC_Pawn, ECollisionResponse::ECR_Overlap);

    // Create Niagara component for flow effects using UE 5.6 VFX system
    FlowVFX = CreateDefaultSubobject<UNiagaraComponent>(TEXT("FlowVFX"));
    FlowVFX->SetupAttachment(RootComponent);
    FlowVFX->SetAutoActivate(false);

    // Create audio component for flow sounds using UE 5.6 audio system
    FlowAudioComponent = CreateDefaultSubobject<UAudioComponent>(TEXT("FlowAudioComponent"));
    FlowAudioComponent->SetupAttachment(RootComponent);
    FlowAudioComponent->SetAutoActivate(false);

    // Create PCG components for procedural generation using UE 5.6 PCG system
    FlowPCGComponents.Reserve(3); // One for each layer
    for (int32 i = 0; i < 3; i++)
    {
        FString ComponentName = FString::Printf(TEXT("FlowPCG_Layer_%d"), i);
        UPCGComponent* PCGComponent = CreateDefaultSubobject<UPCGComponent>(*ComponentName);
        // PCGComponent doesn't need SetupAttachment as it's an ActorComponent
        FlowPCGComponents.Add(PCGComponent);
    }

    // Initialize flow configuration with UE 5.6 optimized defaults
    FlowConfig.TotalLength = 10000.0f;
    FlowConfig.CurveCount = 8;
    FlowConfig.CurveAmplitude = 2000.0f;
    FlowConfig.PatternChangeInterval = 600.0f; // 10 minutes
    FlowConfig.BaseFlowSpeed = 500.0f;
    FlowConfig.FlowSpeedVariation = 200.0f;
    FlowConfig.EnergyIntensity = 1.0f;

    // Initialize island counts based on design specification
    IslandCounts.Add(EPrismalIslandType::Nexus, 5);
    IslandCounts.Add(EPrismalIslandType::Santuario, 8);
    IslandCounts.Add(EPrismalIslandType::Arsenal, 6);
    IslandCounts.Add(EPrismalIslandType::Caos, 4);

    // Initialize state variables
    CurrentFlowSpeed = FlowConfig.BaseFlowSpeed;
    LastPatternChange = 0.0f;
    CurrentPatternSeed = FMath::RandRange(1000, 9999);
    bIsInitialized = false;
    bFlowActive = false;
    LastUpdateTime = 0.0f;
    LastPerformanceUpdate = 0.0f;
    ActiveSegmentCount = 0;
    VisibleIslandCount = 0;

    // Set default flow mesh and materials using UE 5.6 engine defaults
    // Use engine default cube mesh as fallback for flow visualization
    static const FSoftObjectPath DefaultMeshPath(TEXT("/Engine/BasicShapes/Cube.Cube"));
    FlowMesh = Cast<UStaticMesh>(DefaultMeshPath.TryLoad());

    // Use engine default material as fallback
    static const FSoftObjectPath DefaultMaterialPath(TEXT("/Engine/EngineMaterials/DefaultMaterial.DefaultMaterial"));
    FlowMaterial = Cast<UMaterialInterface>(DefaultMaterialPath.TryLoad());

    // Try to load custom assets, but don't fail if they don't exist
    static const FSoftObjectPath FlowMeshPath(TEXT("/Game/Environment/Flow/SM_PrismalFlowSegment.SM_PrismalFlowSegment"));
    if (UStaticMesh* CustomMesh = Cast<UStaticMesh>(FlowMeshPath.TryLoad()))
    {
        FlowMesh = CustomMesh;
    }

    static const FSoftObjectPath FlowMaterialPath(TEXT("/Game/Materials/Flow/M_PrismalFlowDynamic.M_PrismalFlowDynamic"));
    if (UMaterialInterface* CustomMaterial = Cast<UMaterialInterface>(FlowMaterialPath.TryLoad()))
    {
        FlowMaterial = CustomMaterial;
    }

    static const FSoftObjectPath FlowEffectPath(TEXT("/Game/VFX/Flow/NS_PrismalFlowEnergy.NS_PrismalFlowEnergy"));
    FlowEffect = Cast<UNiagaraSystem>(FlowEffectPath.TryLoad());

    static const FSoftObjectPath FlowAudioPath(TEXT("/Game/Audio/Flow/MS_PrismalFlowAmbient.MS_PrismalFlowAmbient"));
    FlowAudio = Cast<USoundBase>(FlowAudioPath.TryLoad());
}

void AAuracronPrismalFlow::BeginPlay()
{
    Super::BeginPlay();

    // Initialize Prismal Flow system using UE 5.6 initialization patterns
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Prismal Flow BeginPlay - Initializing..."));

    if (GetWorld())
    {
        // Delay initialization to ensure all systems are ready
        GetWorld()->GetTimerManager().SetTimerForNextTick([this]()
        {
            InitializeFlow();
        });
    }
}

void AAuracronPrismalFlow::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Clean up Prismal Flow system using UE 5.6 cleanup patterns
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Prismal Flow EndPlay - Cleaning up..."));

    // Clear all timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearAllTimersForObject(this);
    }

    // Clean up spawned islands
    for (AAuracronPrismalIsland* Island : SpawnedIslands)
    {
        if (Island && IsValid(Island))
        {
            Island->Destroy();
        }
    }
    SpawnedIslands.Empty();

    // Clean up dynamic materials
    DynamicMaterials.Empty();

    bIsInitialized = false;
    bFlowActive = false;

    Super::EndPlay(EndPlayReason);
}

void AAuracronPrismalFlow::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    if (!bIsInitialized || !bFlowActive)
    {
        return;
    }

    // Update flow dynamics using UE 5.6 tick optimization
    LastUpdateTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;

    // Update flow dynamics
    UpdateFlowDynamics(DeltaTime);

    // Update island states
    UpdateIslandStates(DeltaTime);

    // Check for pattern regeneration
    if (LastUpdateTime - LastPatternChange > FlowConfig.PatternChangeInterval)
    {
        RegenerateFlowPattern();
    }

    // Performance optimization updates
    if (LastUpdateTime - LastPerformanceUpdate > 5.0f) // Every 5 seconds
    {
        OptimizeFlowRendering();
        LastPerformanceUpdate = LastUpdateTime;
    }
}

// === Flow Management Implementation ===

void AAuracronPrismalFlow::InitializeFlow()
{
    if (bIsInitialized)
    {
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing Prismal Flow system..."));

    // Generate initial flow pattern using UE 5.6 spline generation
    GenerateFlowSpline();

    // Create flow meshes and materials
    CreateFlowMeshes();

    // Setup visual and audio effects
    SetupFlowEffects();
    ConfigureFlowAudio();

    // Spawn strategic islands
    SpawnPrismalIslands();

    // Initialize flow segments
    InitializeFlowSegments();

    // Start flow dynamics
    bFlowActive = true;
    bIsInitialized = true;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Prismal Flow initialized successfully"));
}

void AAuracronPrismalFlow::RegenerateFlowPattern()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Regenerate flow pattern using UE 5.6 procedural regeneration
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Regenerating Prismal Flow pattern..."));

    // Generate new pattern seed
    CurrentPatternSeed = FMath::RandRange(1000, 9999);
    LastPatternChange = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;

    // Regenerate spline with new pattern
    GenerateFlowSpline();

    // Update flow meshes to match new pattern
    CreateFlowMeshes();

    // Reposition islands to optimal locations
    RepositionIslandsForNewPattern();

    // Play pattern transition effects
    PlayFlowTransitionEffect();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Flow pattern regenerated with seed %d"), CurrentPatternSeed);
}

void AAuracronPrismalFlow::UpdateFlowDynamics(float DeltaTime)
{
    if (!bFlowActive)
    {
        return;
    }

    // Update flow dynamics using UE 5.6 dynamic systems
    
    // Update flow speed with variation
    float SpeedVariation = FMath::Sin(LastUpdateTime * 0.5f) * FlowConfig.FlowSpeedVariation;
    CurrentFlowSpeed = FlowConfig.BaseFlowSpeed + SpeedVariation;

    // Update flow segments
    for (FPrismalFlowSegment& Segment : FlowSegments)
    {
        Segment.FlowSpeed = CurrentFlowSpeed;
        
        // Update segment flow direction based on spline
        if (FlowSpline && FlowSpline->GetNumberOfSplinePoints() > 1)
        {
            float SegmentDistance = GetDistanceAlongSpline(Segment.StartLocation);
            Segment.FlowDirection = FlowSpline->GetDirectionAtDistanceAlongSpline(SegmentDistance, ESplineCoordinateSpace::World);
        }
    }

    // Update visual effects
    UpdateFlowVisuals(DeltaTime);

    // Update audio based on flow state
    UpdateFlowAudio();
}

void AAuracronPrismalFlow::SetFlowSpeed(float NewSpeed)
{
    // Set flow speed using UE 5.6 parameter system
    CurrentFlowSpeed = FMath::Clamp(NewSpeed, 100.0f, 2000.0f);
    FlowConfig.BaseFlowSpeed = CurrentFlowSpeed;

    // Update VFX parameters
    if (FlowVFX && FlowVFX->IsActive())
    {
        FlowVFX->SetVariableFloat(FName("FlowSpeed"), CurrentFlowSpeed);
        FlowVFX->SetVariableFloat(FName("SpeedMultiplier"), CurrentFlowSpeed / 500.0f);
    }

    // Update audio parameters
    if (FlowAudioComponent && FlowAudioComponent->IsPlaying())
    {
        FlowAudioComponent->SetFloatParameter(TEXT("FlowSpeed"), CurrentFlowSpeed);
        FlowAudioComponent->SetFloatParameter(TEXT("FlowIntensity"), CurrentFlowSpeed / 1000.0f);
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Flow speed set to %.1f"), CurrentFlowSpeed);
}

// === Team Control Implementation ===

void AAuracronPrismalFlow::SetSegmentControl(int32 SegmentIndex, int32 TeamID)
{
    if (!FlowSegments.IsValidIndex(SegmentIndex))
    {
        return;
    }

    // Set segment control using UE 5.6 team system
    FPrismalFlowSegment& Segment = FlowSegments[SegmentIndex];
    int32 PreviousTeam = Segment.ControllingTeam;
    Segment.ControllingTeam = TeamID;

    // Update segment color based on team
    switch (TeamID)
    {
        case 0: // Neutral
            Segment.SegmentColor = FLinearColor::White;
            break;
        case 1: // Team A
            Segment.SegmentColor = FLinearColor::Blue;
            break;
        case 2: // Team B
            Segment.SegmentColor = FLinearColor::Red;
            break;
        default:
            Segment.SegmentColor = FLinearColor::Gray;
            break;
    }

    // Apply visual changes
    UpdateFlowColors();

    // Apply team control effects to islands in segment
    for (AAuracronPrismalIsland* Island : Segment.Islands)
    {
        if (Island)
        {
            Island->SetControllingTeam(TeamID);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Segment %d control changed from team %d to team %d"), 
        SegmentIndex, PreviousTeam, TeamID);
}

int32 AAuracronPrismalFlow::GetSegmentControl(int32 SegmentIndex) const
{
    if (FlowSegments.IsValidIndex(SegmentIndex))
    {
        return FlowSegments[SegmentIndex].ControllingTeam;
    }
    return 0; // Neutral
}

void AAuracronPrismalFlow::UpdateFlowColors()
{
    // Update flow colors based on team control using UE 5.6 material system
    for (int32 i = 0; i < DynamicMaterials.Num(); i++)
    {
        if (UMaterialInstanceDynamic* DynamicMaterial = DynamicMaterials[i])
        {
            if (FlowSegments.IsValidIndex(i))
            {
                const FPrismalFlowSegment& Segment = FlowSegments[i];
                
                // Set team color
                DynamicMaterial->SetVectorParameterValue(TEXT("TeamColor"), Segment.SegmentColor);
                
                // Set flow intensity based on control
                float ControlIntensity = (Segment.ControllingTeam == 0) ? 0.7f : 1.2f;
                DynamicMaterial->SetScalarParameterValue(TEXT("FlowIntensity"), ControlIntensity);
                
                // Set flow speed parameter
                DynamicMaterial->SetScalarParameterValue(TEXT("FlowSpeed"), Segment.FlowSpeed / 1000.0f);
            }
        }
    }

    // Update VFX colors
    if (FlowVFX && FlowVFX->IsActive())
    {
        // Calculate dominant team color
        FLinearColor DominantColor = CalculateDominantTeamColor();
        FlowVFX->SetVariableLinearColor(FName("FlowColor"), DominantColor);
        FlowVFX->SetVariableFloat(FName("TeamControlStrength"), CalculateTeamControlStrength());
    }
}

// === Island Management Implementation ===

void AAuracronPrismalFlow::SpawnPrismalIslands()
{
    if (!GetWorld())
    {
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Spawning Prismal Islands..."));

    // Clear existing islands
    for (AAuracronPrismalIsland* Island : SpawnedIslands)
    {
        if (Island && IsValid(Island))
        {
            Island->Destroy();
        }
    }
    SpawnedIslands.Empty();

    // Spawn islands by type using UE 5.6 spawning system
    for (const auto& IslandCountPair : IslandCounts)
    {
        EPrismalIslandType IslandType = IslandCountPair.Key;
        int32 Count = IslandCountPair.Value;
        
        PlaceIslandsByType(IslandType, Count);
    }

    // Assign islands to flow segments
    AssignIslandsToSegments();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Spawned %d Prismal Islands total"), SpawnedIslands.Num());
}

void AAuracronPrismalFlow::UpdateIslandStates(float DeltaTime)
{
    // Update all island states using UE 5.6 update system
    for (AAuracronPrismalIsland* Island : SpawnedIslands)
    {
        if (Island && IsValid(Island))
        {
            // Update island based on flow state
            UpdateIslandFlowInteraction(Island, DeltaTime);
            
            // Update island team control
            UpdateIslandTeamControl(Island);
        }
    }
}

TArray<AAuracronPrismalIsland*> AAuracronPrismalFlow::GetIslandsByType(EPrismalIslandType IslandType) const
{
    TArray<AAuracronPrismalIsland*> TypedIslands;
    
    for (AAuracronPrismalIsland* Island : SpawnedIslands)
    {
        if (Island && Island->GetIslandType() == IslandType)
        {
            TypedIslands.Add(Island);
        }
    }
    
    return TypedIslands;
}

AAuracronPrismalIsland* AAuracronPrismalFlow::GetNearestIsland(const FVector& Location, EPrismalIslandType IslandType) const
{
    AAuracronPrismalIsland* NearestIsland = nullptr;
    float NearestDistance = FLT_MAX;
    
    TArray<AAuracronPrismalIsland*> TypedIslands = GetIslandsByType(IslandType);
    
    for (AAuracronPrismalIsland* Island : TypedIslands)
    {
        if (Island)
        {
            float Distance = FVector::Dist(Island->GetActorLocation(), Location);
            if (Distance < NearestDistance)
            {
                NearestDistance = Distance;
                NearestIsland = Island;
            }
        }
    }
    
    return NearestIsland;
}

// === Flow Interaction Implementation ===

bool AAuracronPrismalFlow::IsLocationInFlow(const FVector& Location) const
{
    return IsLocationNearFlow(Location, 500.0f); // 500 unit tolerance
}

FVector AAuracronPrismalFlow::GetFlowVelocityAtLocation(const FVector& Location) const
{
    if (!FlowSpline || !IsLocationInFlow(Location))
    {
        return FVector::ZeroVector;
    }

    // Calculate flow velocity using UE 5.6 spline system
    FVector ClosestPoint = GetClosestPointOnFlow(Location);
    float Distance = GetDistanceAlongSpline(ClosestPoint);
    
    FVector FlowDirection = FlowSpline->GetDirectionAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
    return FlowDirection * CurrentFlowSpeed;
}

float AAuracronPrismalFlow::GetFlowIntensityAtLocation(const FVector& Location) const
{
    if (!IsLocationInFlow(Location))
    {
        return 0.0f;
    }

    // Calculate flow intensity based on distance from center using UE 5.6 calculation system
    FVector ClosestPoint = GetClosestPointOnFlow(Location);
    float DistanceFromCenter = FVector::Dist(Location, ClosestPoint);
    
    // Intensity decreases with distance from flow center
    float MaxDistance = 500.0f; // Flow width
    float IntensityFalloff = FMath::Clamp(1.0f - (DistanceFromCenter / MaxDistance), 0.0f, 1.0f);
    
    return FlowConfig.EnergyIntensity * IntensityFalloff;
}

void AAuracronPrismalFlow::ApplyFlowEffectToActor(AActor* Actor)
{
    if (!Actor || !IsLocationInFlow(Actor->GetActorLocation()))
    {
        return;
    }

    // Apply flow effects to actor using UE 5.6 effect system
    if (APawn* Pawn = Cast<APawn>(Actor))
    {
        if (UAbilitySystemComponent* ASC = Pawn->FindComponentByClass<UAbilitySystemComponent>())
        {
            FGameplayEffectContextHandle EffectContext = ASC->MakeEffectContext();
            EffectContext.AddSourceObject(this);

            // Apply flow movement effect
            static const FSoftClassPath FlowEffectPath(TEXT("/Game/GameplayEffects/Flow/GE_PrismalFlowMovement.GE_PrismalFlowMovement_C"));
            if (TSubclassOf<UGameplayEffect> FlowEffectClass = FlowEffectPath.TryLoadClass<UGameplayEffect>())
            {
                if (UGameplayEffect* FlowGameplayEffect = FlowEffectClass.GetDefaultObject())
                {
                    FGameplayEffectSpec FlowSpec(FlowGameplayEffect, EffectContext, 1.0f);
                
                // Set flow parameters
                FVector FlowVelocity = GetFlowVelocityAtLocation(Actor->GetActorLocation());
                float FlowIntensity = GetFlowIntensityAtLocation(Actor->GetActorLocation());
                
                FlowSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Flow.Speed.Bonus")), FlowVelocity.Size() / 1000.0f);
                FlowSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Flow.Energy.Bonus")), FlowIntensity);
                FlowSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Flow.Direction.X")), FlowVelocity.X);
                FlowSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Flow.Direction.Y")), FlowVelocity.Y);
                
                FlowSpec.SetDuration(5.0f, false); // 5 second effect
                
                ASC->ApplyGameplayEffectSpecToSelf(FlowSpec);
                }
            }
        }
    }
}

// === Visual Effects Implementation ===

void AAuracronPrismalFlow::UpdateFlowVisuals(float DeltaTime)
{
    if (!bFlowActive)
    {
        return;
    }

    // Update flow visuals using UE 5.6 visual update system

    // Update material parameters
    UpdateFlowMaterials();

    // Update VFX parameters
    UpdateFlowEffects();

    // Update flow intensity based on activity
    float ActivityLevel = CalculateFlowActivityLevel();
    SetFlowIntensity(ActivityLevel);
}

void AAuracronPrismalFlow::SetFlowIntensity(float Intensity)
{
    // Set flow intensity using UE 5.6 intensity system
    FlowConfig.EnergyIntensity = FMath::Clamp(Intensity, 0.1f, 3.0f);

    // Update VFX intensity
    if (FlowVFX && FlowVFX->IsActive())
    {
        FlowVFX->SetVariableFloat(FName("EnergyIntensity"), FlowConfig.EnergyIntensity);
        FlowVFX->SetVariableFloat(FName("ParticleCount"), FlowConfig.EnergyIntensity * 100.0f);
        FlowVFX->SetVariableFloat(FName("EmissionRate"), FlowConfig.EnergyIntensity * 50.0f);
    }

    // Update material intensity
    for (UMaterialInstanceDynamic* DynamicMaterial : DynamicMaterials)
    {
        if (DynamicMaterial)
        {
            DynamicMaterial->SetScalarParameterValue(TEXT("EnergyIntensity"), FlowConfig.EnergyIntensity);
            DynamicMaterial->SetScalarParameterValue(TEXT("EmissiveStrength"), FlowConfig.EnergyIntensity * 2.0f);
        }
    }

    // Update audio intensity
    if (FlowAudioComponent && FlowAudioComponent->IsPlaying())
    {
        FlowAudioComponent->SetFloatParameter(TEXT("EnergyIntensity"), FlowConfig.EnergyIntensity);
        FlowAudioComponent->SetVolumeMultiplier(FlowConfig.EnergyIntensity * 0.8f);
    }
}

void AAuracronPrismalFlow::PlayFlowTransitionEffect()
{
    // Play flow transition effect using UE 5.6 transition system
    static const FSoftObjectPath TransitionVFXPath(TEXT("/Game/VFX/Flow/NS_FlowPatternTransition.NS_FlowPatternTransition"));
    if (UNiagaraSystem* TransitionVFX = Cast<UNiagaraSystem>(TransitionVFXPath.TryLoad()))
    {
        // Spawn transition effect at multiple points along the flow
        int32 EffectCount = FMath::Min(5, FlowSpline->GetNumberOfSplinePoints());

        for (int32 i = 0; i < EffectCount; i++)
        {
            float SplineProgress = static_cast<float>(i) / static_cast<float>(EffectCount - 1);
            FVector EffectLocation = FlowSpline->GetLocationAtSplineInputKey(SplineProgress * (FlowSpline->GetNumberOfSplinePoints() - 1), ESplineCoordinateSpace::World);

            UNiagaraComponent* TransitionComponent = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                GetWorld(),
                TransitionVFX,
                EffectLocation,
                FRotator::ZeroRotator,
                FVector::OneVector,
                true, // Auto-destroy
                true, // Auto-activate
                ENCPoolMethod::None
            );

            if (TransitionComponent)
            {
                TransitionComponent->SetVariableFloat(FName("TransitionIntensity"), FlowConfig.EnergyIntensity);
                TransitionComponent->SetVariableLinearColor(FName("TransitionColor"), CalculateDominantTeamColor());
            }
        }
    }

    // Play transition audio
    static const FSoftObjectPath TransitionAudioPath(TEXT("/Game/Audio/Flow/MS_FlowTransition.MS_FlowTransition"));
    if (USoundBase* TransitionAudio = Cast<USoundBase>(TransitionAudioPath.TryLoad()))
    {
        UGameplayStatics::PlaySoundAtLocation(
            GetWorld(),
            TransitionAudio,
            GetActorLocation(),
            1.0f, // Volume
            1.0f, // Pitch
            0.0f, // Start time
            nullptr, // Attenuation
            nullptr // Concurrency
        );
    }
}

// === Debug Functions Implementation ===

void AAuracronPrismalFlow::DebugShowFlowPath()
{
    if (!FlowSpline)
    {
        return;
    }

    // Debug show flow path using UE 5.6 debug drawing
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Debug showing flow path..."));

    if (GetWorld())
    {
        // Draw spline points
        for (int32 i = 0; i < FlowSpline->GetNumberOfSplinePoints(); i++)
        {
            FVector PointLocation = FlowSpline->GetLocationAtSplinePoint(i, ESplineCoordinateSpace::World);

            DrawDebugSphere(
                GetWorld(),
                PointLocation,
                100.0f,
                12,
                FColor::Cyan,
                false, // Persistent
                10.0f, // Duration
                0, // Depth priority
                5.0f // Thickness
            );

            // Draw point index
            DrawDebugString(
                GetWorld(),
                PointLocation + FVector(0, 0, 150),
                FString::Printf(TEXT("Point %d"), i),
                nullptr,
                FColor::White,
                10.0f
            );
        }

        // Draw flow segments
        for (int32 i = 0; i < FlowSegments.Num(); i++)
        {
            const FPrismalFlowSegment& Segment = FlowSegments[i];

            FColor SegmentColor = FColor::White;
            switch (Segment.ControllingTeam)
            {
                case 1: SegmentColor = FColor::Blue; break;
                case 2: SegmentColor = FColor::Red; break;
                default: SegmentColor = FColor::White; break;
            }

            DrawDebugLine(
                GetWorld(),
                Segment.StartLocation,
                Segment.EndLocation,
                SegmentColor,
                false, // Persistent
                10.0f, // Duration
                0, // Depth priority
                10.0f // Thickness
            );
        }
    }
}

void AAuracronPrismalFlow::DebugShowIslandLocations()
{
    if (!GetWorld())
    {
        return;
    }

    // Debug show island locations using UE 5.6 debug system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Debug showing island locations..."));

    for (AAuracronPrismalIsland* Island : SpawnedIslands)
    {
        if (Island)
        {
            FVector IslandLocation = Island->GetActorLocation();
            EPrismalIslandType IslandType = Island->GetIslandType();

            FColor IslandColor = FColor::White;
            switch (IslandType)
            {
                case EPrismalIslandType::Nexus:
                    IslandColor = FColor(255, 215, 0); // Gold color
                    break;
                case EPrismalIslandType::Santuario:
                    IslandColor = FColor::Green;
                    break;
                case EPrismalIslandType::Arsenal:
                    IslandColor = FColor::Orange;
                    break;
                case EPrismalIslandType::Caos:
                    IslandColor = FColor::Purple;
                    break;
            }

            DrawDebugSphere(
                GetWorld(),
                IslandLocation,
                Island->GetIslandRadius(),
                16,
                IslandColor,
                false, // Persistent
                15.0f, // Duration
                0, // Depth priority
                3.0f // Thickness
            );

            // Draw island type label
            DrawDebugString(
                GetWorld(),
                IslandLocation + FVector(0, 0, 200),
                UEnum::GetValueAsString(IslandType),
                nullptr,
                IslandColor,
                15.0f
            );
        }
    }
}

void AAuracronPrismalFlow::DebugRegenerateFlow()
{
    // Debug regenerate flow using UE 5.6 debug system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Debug regenerating flow pattern..."));

    RegenerateFlowPattern();

    // Show debug visualization
    DebugShowFlowPath();
    DebugShowIslandLocations();
}

// === Private Implementation Methods ===

void AAuracronPrismalFlow::GenerateFlowSpline()
{
    if (!FlowSpline)
    {
        return;
    }

    // Generate serpentine flow spline using UE 5.6 spline generation
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Generating serpentine flow spline..."));

    // Clear existing spline points
    FlowSpline->ClearSplinePoints();

    // Generate serpentine points
    TArray<FVector> SerpentinePoints = GenerateSerpentinePoints();

    // Add points to spline
    for (int32 i = 0; i < SerpentinePoints.Num(); i++)
    {
        FlowSpline->AddSplinePoint(SerpentinePoints[i], ESplineCoordinateSpace::World);
        FlowSpline->SetSplinePointType(i, ESplinePointType::CurveClamped);
    }

    // Apply curve variation for natural flow
    ApplyCurveVariation();

    // Ensure connectivity between layers
    EnsureLayerConnectivity();

    // Update spline tangents for smooth curves
    FlowSpline->UpdateSpline();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Flow spline generated with %d points"), SerpentinePoints.Num());
}

TArray<FVector> AAuracronPrismalFlow::GenerateSerpentinePoints()
{
    // Generate serpentine points using UE 5.6 mathematical generation
    TArray<FVector> Points;
    Points.Reserve(FlowConfig.CurveCount * 3); // 3 points per curve section

    FMath::RandInit(CurrentPatternSeed);

    // Start from Terrestrial layer
    FVector CurrentPoint = FVector(0.0f, -5000.0f, 100.0f); // Terrestrial start
    Points.Add(CurrentPoint);

    float SegmentLength = FlowConfig.TotalLength / (FlowConfig.CurveCount * 2);

    for (int32 i = 0; i < FlowConfig.CurveCount; i++)
    {
        // Calculate curve parameters
        float CurvePhase = static_cast<float>(i) / static_cast<float>(FlowConfig.CurveCount);
        float CurveAngle = FMath::Sin(CurvePhase * PI * 2.0f) * FlowConfig.CurveAmplitude;

        // Add variation based on seed
        float Variation = FMath::PerlinNoise1D(CurvePhase * 10.0f + CurrentPatternSeed * 0.001f) * 500.0f;

        // Generate curve points
        FVector CurveDirection = FVector(1.0f, FMath::Sin(CurvePhase * PI * 4.0f), 0.0f).GetSafeNormal();

        // First curve point
        CurrentPoint += CurveDirection * SegmentLength;
        CurrentPoint.Y += CurveAngle + Variation;

        // Add layer transitions
        if (i % 3 == 1) // Transition to Celestial
        {
            CurrentPoint.Z = 2500.0f;
        }
        else if (i % 3 == 2) // Transition to Abyssal
        {
            CurrentPoint.Z = -800.0f;
        }
        else // Back to Terrestrial
        {
            CurrentPoint.Z = 100.0f;
        }

        Points.Add(CurrentPoint);

        // Second curve point (control point)
        FVector ControlPoint = CurrentPoint;
        ControlPoint += FVector(SegmentLength * 0.5f, CurveAngle * 0.5f, 0.0f);
        Points.Add(ControlPoint);
    }

    // End point
    FVector EndPoint = FVector(FlowConfig.TotalLength, 0.0f, 100.0f);
    Points.Add(EndPoint);

    return Points;
}

void AAuracronPrismalFlow::ApplyCurveVariation()
{
    if (!FlowSpline)
    {
        return;
    }

    // Apply curve variation using UE 5.6 spline manipulation
    for (int32 i = 1; i < FlowSpline->GetNumberOfSplinePoints() - 1; i++)
    {
        // Add random variation to tangents for natural flow
        FVector InTangent = FlowSpline->GetTangentAtSplinePoint(i, ESplineCoordinateSpace::World);
        FVector OutTangent = InTangent;

        // Apply variation
        float VariationStrength = 200.0f;
        FVector Variation = FVector(
            FMath::RandRange(-VariationStrength, VariationStrength),
            FMath::RandRange(-VariationStrength, VariationStrength),
            FMath::RandRange(-VariationStrength * 0.5f, VariationStrength * 0.5f)
        );

        InTangent += Variation;
        OutTangent += Variation * 0.8f; // Slightly different for asymmetry

        FlowSpline->SetTangentsAtSplinePoint(i, InTangent, OutTangent, ESplineCoordinateSpace::World);
    }
}

void AAuracronPrismalFlow::EnsureLayerConnectivity()
{
    if (!FlowSpline)
    {
        return;
    }

    // Ensure flow connects all three layers using UE 5.6 connectivity system
    bool bHasTerrestrial = false;
    bool bHasCelestial = false;
    bool bHasAbyssal = false;

    // Check which layers are covered
    for (int32 i = 0; i < FlowSpline->GetNumberOfSplinePoints(); i++)
    {
        FVector PointLocation = FlowSpline->GetLocationAtSplinePoint(i, ESplineCoordinateSpace::World);

        if (PointLocation.Z > 2000.0f)
        {
            bHasCelestial = true;
        }
        else if (PointLocation.Z < -500.0f)
        {
            bHasAbyssal = true;
        }
        else
        {
            bHasTerrestrial = true;
        }
    }

    // Add missing layer connections
    if (!bHasCelestial)
    {
        AddLayerConnectionPoint(2500.0f); // Celestial
    }

    if (!bHasAbyssal)
    {
        AddLayerConnectionPoint(-800.0f); // Abyssal
    }

    if (!bHasTerrestrial)
    {
        AddLayerConnectionPoint(100.0f); // Terrestrial
    }
}

void AAuracronPrismalFlow::AddLayerConnectionPoint(float TargetZ)
{
    if (!FlowSpline)
    {
        return;
    }

    // Add layer connection point using UE 5.6 spline modification
    int32 InsertIndex = FlowSpline->GetNumberOfSplinePoints() / 2; // Insert in middle
    FVector InsertLocation = FlowSpline->GetLocationAtSplinePoint(InsertIndex, ESplineCoordinateSpace::World);
    InsertLocation.Z = TargetZ;

    FlowSpline->AddSplinePointAtIndex(InsertLocation, InsertIndex, ESplineCoordinateSpace::World);
    FlowSpline->SetSplinePointType(InsertIndex, ESplinePointType::CurveClamped);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Added layer connection point at Z=%.1f"), TargetZ);
}

void AAuracronPrismalFlow::CreateFlowMeshes()
{
    if (!FlowMesh || !FlowMaterial)
    {
        return;
    }

    // Create flow meshes using UE 5.6 mesh generation
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Creating flow meshes..."));

    // Set mesh and material
    FlowMeshComponent->SetStaticMesh(FlowMesh);

    // Create dynamic material instance
    UMaterialInstanceDynamic* DynamicFlowMaterial = FlowMeshComponent->CreateDynamicMaterialInstance(0, FlowMaterial);
    if (DynamicFlowMaterial)
    {
        DynamicMaterials.Add(DynamicFlowMaterial);

        // Set initial material parameters
        DynamicFlowMaterial->SetScalarParameterValue(TEXT("FlowSpeed"), CurrentFlowSpeed / 1000.0f);
        DynamicFlowMaterial->SetScalarParameterValue(TEXT("EnergyIntensity"), FlowConfig.EnergyIntensity);
        DynamicFlowMaterial->SetVectorParameterValue(TEXT("FlowColor"), FLinearColor::White);
    }

    // Generate mesh segments along spline using PCG
    GenerateFlowMeshSegments();
}

void AAuracronPrismalFlow::GenerateFlowMeshSegments()
{
    if (!FlowSpline || FlowPCGComponents.IsEmpty())
    {
        return;
    }

    // Generate flow mesh segments using UE 5.6 PCG system
    for (int32 LayerIndex = 0; LayerIndex < FlowPCGComponents.Num(); LayerIndex++)
    {
        UPCGComponent* PCGComponent = FlowPCGComponents[LayerIndex];
        if (!PCGComponent)
        {
            continue;
        }

        // Configure PCG for flow generation - only if custom graph exists
        static const FSoftObjectPath FlowPCGGraphPath(TEXT("/Game/PCG/Flow/PCG_PrismalFlowGeneration.PCG_PrismalFlowGeneration"));
        if (UPCGGraph* FlowPCGGraph = Cast<UPCGGraph>(FlowPCGGraphPath.TryLoad()))
        {
            PCGComponent->SetGraph(FlowPCGGraph);

            // PCG parameters are set through the graph interface in UE 5.6
            // Parameters would be configured in the PCG graph asset itself

            // Generate PCG content
            PCGComponent->Generate();

            UE_LOG(LogTemp, Log, TEXT("AURACRON: PCG Flow generation configured for layer %d"), LayerIndex);
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: PCG Flow graph not found, skipping PCG generation for layer %d"), LayerIndex);
        }
    }
}

void AAuracronPrismalFlow::SetupFlowEffects()
{
    if (!FlowEffect)
    {
        return;
    }

    // Setup flow effects using UE 5.6 VFX system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Setting up flow effects..."));

    // Configure Niagara component
    FlowVFX->SetAsset(FlowEffect);

    // Set initial VFX parameters
    FlowVFX->SetVariableFloat(FName("FlowSpeed"), CurrentFlowSpeed);
    FlowVFX->SetVariableFloat(FName("EnergyIntensity"), FlowConfig.EnergyIntensity);
    FlowVFX->SetVariableLinearColor(FName("FlowColor"), FLinearColor::White);
    FlowVFX->SetVariableFloat(FName("FlowLength"), FlowConfig.TotalLength);
    FlowVFX->SetVariableInt(FName("CurveCount"), FlowConfig.CurveCount);

    // Activate VFX
    FlowVFX->Activate();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Flow effects configured and activated"));
}

void AAuracronPrismalFlow::ConfigureFlowAudio()
{
    if (!FlowAudio)
    {
        return;
    }

    // Configure flow audio using UE 5.6 audio system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Configuring flow audio..."));

    // Set audio asset
    FlowAudioComponent->SetSound(FlowAudio);

    // Configure audio parameters
    FlowAudioComponent->SetFloatParameter(TEXT("FlowSpeed"), CurrentFlowSpeed);
    FlowAudioComponent->SetFloatParameter(TEXT("EnergyIntensity"), FlowConfig.EnergyIntensity);
    FlowAudioComponent->SetFloatParameter(TEXT("FlowLength"), FlowConfig.TotalLength / 1000.0f);

    // Set audio properties
    FlowAudioComponent->SetVolumeMultiplier(0.8f);
    FlowAudioComponent->SetPitchMultiplier(1.0f);
    FlowAudioComponent->bAutoActivate = true;

    // Start playing
    FlowAudioComponent->Play();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Flow audio configured and started"));
}

void AAuracronPrismalFlow::PlaceIslandsByType(EPrismalIslandType IslandType, int32 Count)
{
    if (!GetWorld() || Count <= 0)
    {
        return;
    }

    // Place islands by type using UE 5.6 placement system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Placing %d islands of type %s"), Count, *UEnum::GetValueAsString(IslandType));

    for (int32 i = 0; i < Count; i++)
    {
        // Find optimal location for island
        FVector IslandLocation = FindOptimalIslandLocation(IslandType);

        // Validate placement
        float IslandRadius = GetIslandRadiusForType(IslandType);
        if (!ValidateIslandPlacement(IslandLocation, IslandRadius))
        {
            // Try alternative locations
            for (int32 Attempt = 0; Attempt < 5; Attempt++)
            {
                IslandLocation = FindOptimalIslandLocation(IslandType);
                if (ValidateIslandPlacement(IslandLocation, IslandRadius))
                {
                    break;
                }
            }
        }

        // Spawn island
        FActorSpawnParameters SpawnParams;
        SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
        SpawnParams.bNoFail = true;
        SpawnParams.Name = FName(*FString::Printf(TEXT("%s_Island_%d"), *UEnum::GetValueAsString(IslandType), i));

        if (AAuracronPrismalIsland* NewIsland = GetWorld()->SpawnActor<AAuracronPrismalIsland>(IslandLocation, FRotator::ZeroRotator, SpawnParams))
        {
            // Configure island
            NewIsland->SetIslandType(IslandType);
            NewIsland->SetIslandRadius(IslandRadius);
            NewIsland->InitializeIsland();

            SpawnedIslands.Add(NewIsland);

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Spawned %s island at %s"),
                *UEnum::GetValueAsString(IslandType), *IslandLocation.ToString());
        }
    }
}

FVector AAuracronPrismalFlow::FindOptimalIslandLocation(EPrismalIslandType IslandType) const
{
    if (!FlowSpline || FlowSpline->GetNumberOfSplinePoints() < 2)
    {
        return FVector::ZeroVector;
    }

    // Find optimal island location using UE 5.6 optimization algorithms
    float SplineLength = FlowSpline->GetSplineLength();

    // Island type-specific placement strategy
    float PlacementProgress = 0.0f;

    switch (IslandType)
    {
        case EPrismalIslandType::Nexus:
            // Nexus islands at strategic control points
            PlacementProgress = FMath::RandRange(0.2f, 0.8f);
            break;
        case EPrismalIslandType::Santuario:
            // Santuario islands at safe positions
            PlacementProgress = FMath::RandRange(0.1f, 0.9f);
            break;
        case EPrismalIslandType::Arsenal:
            // Arsenal islands at curve peaks for tactical advantage
            PlacementProgress = FMath::RandRange(0.3f, 0.7f);
            break;
        case EPrismalIslandType::Caos:
            // Caos islands at dangerous positions
            PlacementProgress = FMath::RandRange(0.4f, 0.6f);
            break;
        default:
            PlacementProgress = FMath::RandRange(0.2f, 0.8f);
            break;
    }

    // Get location along spline
    float DistanceAlongSpline = PlacementProgress * SplineLength;
    FVector SplineLocation = FlowSpline->GetLocationAtDistanceAlongSpline(DistanceAlongSpline, ESplineCoordinateSpace::World);

    // Add offset from flow center
    FVector SplineDirection = FlowSpline->GetDirectionAtDistanceAlongSpline(DistanceAlongSpline, ESplineCoordinateSpace::World);
    FVector PerpendicularDirection = FVector::CrossProduct(SplineDirection, FVector::UpVector).GetSafeNormal();

    // Random offset for variety
    float OffsetDistance = FMath::RandRange(300.0f, 800.0f);
    bool bOffsetLeft = FMath::RandBool();
    FVector Offset = PerpendicularDirection * OffsetDistance * (bOffsetLeft ? -1.0f : 1.0f);

    return SplineLocation + Offset;
}

bool AAuracronPrismalFlow::ValidateIslandPlacement(const FVector& Location, float Radius) const
{
    // Validate island placement using UE 5.6 validation system

    // Check distance from other islands
    for (const AAuracronPrismalIsland* ExistingIsland : SpawnedIslands)
    {
        if (ExistingIsland)
        {
            float Distance = FVector::Dist(ExistingIsland->GetActorLocation(), Location);
            float MinDistance = Radius + ExistingIsland->GetIslandRadius() + 200.0f; // 200 unit buffer

            if (Distance < MinDistance)
            {
                return false; // Too close to existing island
            }
        }
    }

    // Check if location is reasonable relative to flow
    if (!IsLocationNearFlow(Location, 1500.0f))
    {
        return false; // Too far from flow
    }

    // Check for terrain conflicts (basic validation)
    if (GetWorld())
    {
        FHitResult HitResult;
        FVector TraceStart = Location + FVector(0, 0, 1000);
        FVector TraceEnd = Location - FVector(0, 0, 1000);

        if (GetWorld()->LineTraceSingleByChannel(HitResult, TraceStart, TraceEnd, ECollisionChannel::ECC_WorldStatic))
        {
            // Check if hit location is reasonable
            float HeightDifference = FMath::Abs(HitResult.Location.Z - Location.Z);
            if (HeightDifference > 500.0f)
            {
                return false; // Terrain too steep or inappropriate
            }
        }
    }

    return true; // Valid placement
}

float AAuracronPrismalFlow::GetIslandRadiusForType(EPrismalIslandType IslandType) const
{
    // Get island radius based on type
    switch (IslandType)
    {
        case EPrismalIslandType::Nexus:
            return 400.0f; // Large control islands
        case EPrismalIslandType::Santuario:
            return 300.0f; // Medium safe zones
        case EPrismalIslandType::Arsenal:
            return 250.0f; // Compact upgrade stations
        case EPrismalIslandType::Caos:
            return 350.0f; // Large chaotic areas
        default:
            return 300.0f;
    }
}

void AAuracronPrismalFlow::InitializeFlowSegments()
{
    if (!FlowSpline)
    {
        return;
    }

    // Initialize flow segments using UE 5.6 segmentation system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing flow segments..."));

    FlowSegments.Empty();

    int32 SegmentCount = FMath::Max(4, FlowSpline->GetNumberOfSplinePoints() - 1);
    FlowSegments.Reserve(SegmentCount);

    float SplineLength = FlowSpline->GetSplineLength();
    float SegmentLength = SplineLength / SegmentCount;

    for (int32 i = 0; i < SegmentCount; i++)
    {
        FPrismalFlowSegment NewSegment;

        // Calculate segment boundaries
        float StartDistance = i * SegmentLength;
        float EndDistance = (i + 1) * SegmentLength;

        NewSegment.StartLocation = FlowSpline->GetLocationAtDistanceAlongSpline(StartDistance, ESplineCoordinateSpace::World);
        NewSegment.EndLocation = FlowSpline->GetLocationAtDistanceAlongSpline(EndDistance, ESplineCoordinateSpace::World);

        // Set segment properties
        NewSegment.Width = 1000.0f;
        NewSegment.FlowSpeed = CurrentFlowSpeed;
        NewSegment.FlowDirection = FlowSpline->GetDirectionAtDistanceAlongSpline(StartDistance, ESplineCoordinateSpace::World);
        NewSegment.ControllingTeam = 0; // Start neutral
        NewSegment.SegmentColor = FLinearColor::White;

        FlowSegments.Add(NewSegment);
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initialized %d flow segments"), FlowSegments.Num());
}

void AAuracronPrismalFlow::AssignIslandsToSegments()
{
    // Assign islands to flow segments using UE 5.6 assignment system
    for (AAuracronPrismalIsland* Island : SpawnedIslands)
    {
        if (!Island)
        {
            continue;
        }

        // Find closest segment for island
        FVector IslandLocation = Island->GetActorLocation();
        int32 ClosestSegmentIndex = GetSegmentIndexAtLocation(IslandLocation);

        if (FlowSegments.IsValidIndex(ClosestSegmentIndex))
        {
            FlowSegments[ClosestSegmentIndex].Islands.Add(Island);
            Island->SetFlowSegmentIndex(ClosestSegmentIndex);
        }
    }
}

void AAuracronPrismalFlow::RepositionIslandsForNewPattern()
{
    // Reposition islands for new flow pattern using UE 5.6 repositioning system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Repositioning islands for new pattern..."));

    for (AAuracronPrismalIsland* Island : SpawnedIslands)
    {
        if (!Island)
        {
            continue;
        }

        // Find new optimal location
        EPrismalIslandType IslandType = Island->GetIslandType();
        FVector NewLocation = FindOptimalIslandLocation(IslandType);

        // Validate new location
        if (ValidateIslandPlacement(NewLocation, Island->GetIslandRadius()))
        {
            // Smoothly move island to new location
            Island->SetActorLocation(NewLocation);

            // Play repositioning effect
            Island->PlayRepositioningEffect();
        }
    }

    // Reassign islands to new segments
    AssignIslandsToSegments();
}

void AAuracronPrismalFlow::UpdateFlowMaterials()
{
    // Update flow materials using UE 5.6 material system
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;

    for (UMaterialInstanceDynamic* DynamicMaterial : DynamicMaterials)
    {
        if (DynamicMaterial)
        {
            // Update time-based parameters
            DynamicMaterial->SetScalarParameterValue(TEXT("GameTime"), CurrentTime);
            DynamicMaterial->SetScalarParameterValue(TEXT("FlowSpeed"), CurrentFlowSpeed / 1000.0f);
            DynamicMaterial->SetScalarParameterValue(TEXT("EnergyIntensity"), FlowConfig.EnergyIntensity);

            // Update flow animation
            float FlowOffset = FMath::Fmod(CurrentTime * (CurrentFlowSpeed / 1000.0f), 1.0f);
            DynamicMaterial->SetScalarParameterValue(TEXT("FlowOffset"), FlowOffset);

            // Update energy pulsing
            float EnergyPulse = (FMath::Sin(CurrentTime * 2.0f) + 1.0f) * 0.5f;
            DynamicMaterial->SetScalarParameterValue(TEXT("EnergyPulse"), EnergyPulse);
        }
    }
}

void AAuracronPrismalFlow::UpdateFlowEffects()
{
    if (!FlowVFX || !FlowVFX->IsActive())
    {
        return;
    }

    // Update flow effects using UE 5.6 VFX update system
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;

    // Update VFX parameters
    FlowVFX->SetVariableFloat(FName("FlowSpeed"), CurrentFlowSpeed);
    FlowVFX->SetVariableFloat(FName("EnergyIntensity"), FlowConfig.EnergyIntensity);
    FlowVFX->SetVariableFloat(FName("GameTime"), CurrentTime);

    // Update particle count based on activity
    float ActivityLevel = CalculateFlowActivityLevel();
    FlowVFX->SetVariableFloat(FName("ParticleMultiplier"), ActivityLevel);

    // Update flow direction visualization
    if (FlowSpline && FlowSpline->GetNumberOfSplinePoints() > 1)
    {
        FVector FlowDirection = FlowSpline->GetDirectionAtDistanceAlongSpline(0.0f, ESplineCoordinateSpace::World);
        FlowVFX->SetVariableVec3(FName("FlowDirection"), FlowDirection);
    }
}

void AAuracronPrismalFlow::UpdateFlowAudio()
{
    if (!FlowAudioComponent || !FlowAudioComponent->IsPlaying())
    {
        return;
    }

    // Update flow audio using UE 5.6 audio update system
    float ActivityLevel = CalculateFlowActivityLevel();

    // Update audio parameters
    FlowAudioComponent->SetFloatParameter(TEXT("FlowSpeed"), CurrentFlowSpeed);
    FlowAudioComponent->SetFloatParameter(TEXT("EnergyIntensity"), FlowConfig.EnergyIntensity);
    FlowAudioComponent->SetFloatParameter(TEXT("ActivityLevel"), ActivityLevel);

    // Update volume based on activity
    float VolumeMultiplier = FMath::Clamp(0.5f + (ActivityLevel * 0.5f), 0.3f, 1.0f);
    FlowAudioComponent->SetVolumeMultiplier(VolumeMultiplier);

    // Update pitch based on flow speed
    float PitchMultiplier = FMath::Clamp(0.8f + ((CurrentFlowSpeed - 500.0f) / 1000.0f), 0.7f, 1.3f);
    FlowAudioComponent->SetPitchMultiplier(PitchMultiplier);
}

float AAuracronPrismalFlow::CalculateFlowActivityLevel() const
{
    // Calculate flow activity level based on player presence and island activity
    float ActivityLevel = 0.5f; // Base activity

    // Add activity based on players in flow
    int32 PlayersInFlow = 0;
    if (GetWorld())
    {
        for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
        {
            APlayerController* PC = Iterator->Get();
            if (PC && PC->GetPawn() && IsLocationInFlow(PC->GetPawn()->GetActorLocation()))
            {
                PlayersInFlow++;
            }
        }
    }

    ActivityLevel += PlayersInFlow * 0.2f; // 20% per player

    // Add activity based on active islands
    int32 ActiveIslands = 0;
    for (const AAuracronPrismalIsland* Island : SpawnedIslands)
    {
        if (Island && Island->IsIslandActive())
        {
            ActiveIslands++;
        }
    }

    ActivityLevel += ActiveIslands * 0.1f; // 10% per active island

    return FMath::Clamp(ActivityLevel, 0.1f, 2.0f);
}

float AAuracronPrismalFlow::GetLayerHeight(int32 LayerIndex) const
{
    // Get height for layer index
    switch (LayerIndex)
    {
        case 0: return 100.0f;   // Terrestrial
        case 1: return 2500.0f;  // Celestial
        case 2: return -800.0f;  // Abyssal
        default: return 100.0f;
    }
}

void AAuracronPrismalFlow::UpdateIslandFlowInteraction(AAuracronPrismalIsland* Island, float DeltaTime)
{
    if (!Island)
    {
        return;
    }

    // Update island flow interaction using UE 5.6 interaction system
    FVector IslandLocation = Island->GetActorLocation();

    // Calculate flow influence on island
    float FlowIntensity = GetFlowIntensityAtLocation(IslandLocation);
    FVector FlowVelocity = GetFlowVelocityAtLocation(IslandLocation);

    // Apply flow effects to island
    Island->SetFlowInfluence(FlowIntensity);
    Island->SetFlowDirection(FlowVelocity.GetSafeNormal());

    // Update island energy based on flow
    float EnergyBonus = FlowIntensity * DeltaTime * 10.0f;
    Island->AddEnergyBonus(EnergyBonus);
}

void AAuracronPrismalFlow::UpdateIslandTeamControl(AAuracronPrismalIsland* Island)
{
    if (!Island)
    {
        return;
    }

    // Update island team control based on flow segment using UE 5.6 control system
    FVector IslandLocation = Island->GetActorLocation();
    int32 SegmentIndex = GetSegmentIndexAtLocation(IslandLocation);

    if (FlowSegments.IsValidIndex(SegmentIndex))
    {
        int32 SegmentTeam = FlowSegments[SegmentIndex].ControllingTeam;

        // Apply team influence to island
        if (SegmentTeam != 0 && Island->GetControllingTeam() != SegmentTeam)
        {
            // Gradual team influence based on flow control
            Island->ApplyTeamInfluence(SegmentTeam, 0.1f); // 10% influence per update
        }
    }
}

void AAuracronPrismalFlow::OptimizeFlowRendering()
{
    if (!GetWorld())
    {
        return;
    }

    // Optimize flow rendering using UE 5.6 optimization system

    // Find primary viewer location
    FVector ViewerLocation = FVector::ZeroVector;
    if (APlayerController* PC = GetWorld()->GetFirstPlayerController())
    {
        if (PC->GetPawn())
        {
            ViewerLocation = PC->GetPawn()->GetActorLocation();
        }
    }

    // Update LOD based on distance
    UpdateFlowLOD(ViewerLocation);

    // Cull distant segments
    CullDistantSegments(ViewerLocation, 15000.0f);

    // Optimize island rendering
    OptimizeIslandRendering(ViewerLocation);
}

void AAuracronPrismalFlow::UpdateFlowLOD(const FVector& ViewerLocation)
{
    // Update flow LOD using UE 5.6 LOD system
    float DistanceToViewer = FVector::Dist(GetActorLocation(), ViewerLocation);

    int32 LODLevel = 0;
    if (DistanceToViewer > 10000.0f)
    {
        LODLevel = 3; // Lowest detail
    }
    else if (DistanceToViewer > 5000.0f)
    {
        LODLevel = 2; // Medium detail
    }
    else if (DistanceToViewer > 2000.0f)
    {
        LODLevel = 1; // High detail
    }
    else
    {
        LODLevel = 0; // Maximum detail
    }

    // Apply LOD to mesh component
    if (FlowMeshComponent)
    {
        FlowMeshComponent->SetForcedLodModel(LODLevel + 1); // UE uses 1-based LOD indexing
    }

    // Adjust VFX quality based on LOD
    if (FlowVFX && FlowVFX->IsActive())
    {
        float QualityMultiplier = FMath::Clamp(1.0f - (LODLevel * 0.25f), 0.25f, 1.0f);
        FlowVFX->SetVariableFloat(FName("QualityLevel"), QualityMultiplier);
        FlowVFX->SetVariableFloat(FName("ParticleCount"), QualityMultiplier * 200.0f);
    }
}

void AAuracronPrismalFlow::CullDistantSegments(const FVector& ViewerLocation, float CullDistance)
{
    // Cull distant segments using UE 5.6 culling system
    ActiveSegmentCount = 0;

    for (int32 i = 0; i < FlowSegments.Num(); i++)
    {
        const FPrismalFlowSegment& Segment = FlowSegments[i];

        // Calculate distance to segment
        FVector SegmentCenter = (Segment.StartLocation + Segment.EndLocation) * 0.5f;
        float DistanceToSegment = FVector::Dist(SegmentCenter, ViewerLocation);

        bool bShouldRender = DistanceToSegment < CullDistance;

        if (bShouldRender)
        {
            ActiveSegmentCount++;
        }

        // Update segment visibility in materials
        if (i < DynamicMaterials.Num() && DynamicMaterials[i])
        {
            DynamicMaterials[i]->SetScalarParameterValue(TEXT("SegmentVisibility"), bShouldRender ? 1.0f : 0.0f);
        }
    }
}

void AAuracronPrismalFlow::OptimizeIslandRendering(const FVector& ViewerLocation)
{
    // Optimize island rendering using UE 5.6 island optimization
    VisibleIslandCount = 0;

    for (AAuracronPrismalIsland* Island : SpawnedIslands)
    {
        if (!Island)
        {
            continue;
        }

        float DistanceToIsland = FVector::Dist(Island->GetActorLocation(), ViewerLocation);

        // Apply distance-based optimization
        if (DistanceToIsland < 20000.0f)
        {
            Island->OptimizeIslandRendering(ViewerLocation);
            VisibleIslandCount++;
        }
        else
        {
            // Hide very distant islands
            Island->SetActorHiddenInGame(true);
        }
    }
}

FLinearColor AAuracronPrismalFlow::CalculateDominantTeamColor() const
{
    // Calculate dominant team color based on segment control
    int32 Team1Segments = 0;
    int32 Team2Segments = 0;
    int32 NeutralSegments = 0;

    for (const FPrismalFlowSegment& Segment : FlowSegments)
    {
        switch (Segment.ControllingTeam)
        {
            case 1: Team1Segments++; break;
            case 2: Team2Segments++; break;
            default: NeutralSegments++; break;
        }
    }

    // Determine dominant color
    if (Team1Segments > Team2Segments && Team1Segments > NeutralSegments)
    {
        return FLinearColor::Blue;
    }
    else if (Team2Segments > Team1Segments && Team2Segments > NeutralSegments)
    {
        return FLinearColor::Red;
    }
    else
    {
        return FLinearColor::White; // Neutral or balanced
    }
}

float AAuracronPrismalFlow::CalculateTeamControlStrength() const
{
    // Calculate team control strength as percentage
    int32 ControlledSegments = 0;

    for (const FPrismalFlowSegment& Segment : FlowSegments)
    {
        if (Segment.ControllingTeam != 0)
        {
            ControlledSegments++;
        }
    }

    return static_cast<float>(ControlledSegments) / FMath::Max(1, FlowSegments.Num());
}

// === Utility Functions Implementation ===

int32 AAuracronPrismalFlow::GetSegmentIndexAtLocation(const FVector& Location) const
{
    if (!FlowSpline || FlowSegments.IsEmpty())
    {
        return 0;
    }

    // Find segment index at location using UE 5.6 spatial queries
    float DistanceAlongSpline = GetDistanceAlongSpline(Location);
    float SplineLength = FlowSpline->GetSplineLength();

    if (SplineLength <= 0.0f)
    {
        return 0;
    }

    float SegmentProgress = DistanceAlongSpline / SplineLength;
    int32 SegmentIndex = FMath::FloorToInt(SegmentProgress * FlowSegments.Num());

    return FMath::Clamp(SegmentIndex, 0, FlowSegments.Num() - 1);
}

float AAuracronPrismalFlow::GetDistanceAlongSpline(const FVector& Location) const
{
    if (!FlowSpline)
    {
        return 0.0f;
    }

    // Get distance along spline using UE 5.6 spline calculations
    FVector ClosestPoint = GetClosestPointOnFlow(Location);
    return FlowSpline->FindInputKeyClosestToWorldLocation(ClosestPoint);
}

FVector AAuracronPrismalFlow::GetClosestPointOnFlow(const FVector& Location) const
{
    if (!FlowSpline)
    {
        return FVector::ZeroVector;
    }

    // Get closest point on flow using UE 5.6 spline proximity
    float InputKey = FlowSpline->FindInputKeyClosestToWorldLocation(Location);
    return FlowSpline->GetLocationAtSplineInputKey(InputKey, ESplineCoordinateSpace::World);
}

bool AAuracronPrismalFlow::IsLocationNearFlow(const FVector& Location, float Tolerance) const
{
    if (!FlowSpline)
    {
        return false;
    }

    // Check if location is near flow using UE 5.6 proximity testing
    FVector ClosestPoint = GetClosestPointOnFlow(Location);
    float Distance = FVector::Dist(Location, ClosestPoint);

    return Distance <= Tolerance;
}

// === Advanced Flow Behavior Implementation ===

void AAuracronPrismalFlow::HandlePlayerFlowEntry(APawn* Player)
{
    if (!Player)
    {
        return;
    }

    // Handle player entering flow using UE 5.6 entry system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Player %s entered Prismal Flow"), *Player->GetName());

    // Apply flow entry effects
    ApplyFlowEffectToActor(Player);

    // Play entry VFX
    static const FSoftObjectPath EntryVFXPath(TEXT("/Game/VFX/Flow/NS_FlowPlayerEntry.NS_FlowPlayerEntry"));
    if (UNiagaraSystem* EntryVFX = Cast<UNiagaraSystem>(EntryVFXPath.TryLoad()))
    {
        UNiagaraFunctionLibrary::SpawnSystemAttached(
            EntryVFX,
            Player->GetRootComponent(),
            NAME_None,
            FVector::ZeroVector,
            FRotator::ZeroRotator,
            EAttachLocation::KeepRelativeOffset,
            true // Auto-destroy
        );
    }

    // Update flow activity
    float NewActivityLevel = CalculateFlowActivityLevel();
    SetFlowIntensity(NewActivityLevel);
}

void AAuracronPrismalFlow::HandlePlayerFlowExit(APawn* Player)
{
    if (!Player)
    {
        return;
    }

    // Handle player exiting flow using UE 5.6 exit system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Player %s exited Prismal Flow"), *Player->GetName());

    // Remove flow effects from player
    if (UAbilitySystemComponent* ASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        // Remove flow movement effects
        FGameplayTagContainer FlowTags;
        FlowTags.AddTag(FGameplayTag::RequestGameplayTag(TEXT("Flow.Movement")));
        ASC->RemoveActiveEffectsWithTags(FlowTags);
    }

    // Update flow activity
    float NewActivityLevel = CalculateFlowActivityLevel();
    SetFlowIntensity(NewActivityLevel);
}

void AAuracronPrismalFlow::ProcessFlowSegmentCapture(int32 SegmentIndex, int32 CapturingTeam)
{
    if (!FlowSegments.IsValidIndex(SegmentIndex))
    {
        return;
    }

    // Process flow segment capture using UE 5.6 capture system
    FPrismalFlowSegment& Segment = FlowSegments[SegmentIndex];
    int32 PreviousTeam = Segment.ControllingTeam;

    if (PreviousTeam != CapturingTeam)
    {
        // Change segment control
        SetSegmentControl(SegmentIndex, CapturingTeam);

        // Play capture effects
        PlaySegmentCaptureEffects(SegmentIndex, CapturingTeam);

        // Award capture rewards to team
        AwardSegmentCaptureRewards(CapturingTeam, SegmentIndex);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Flow segment %d captured by team %d"), SegmentIndex, CapturingTeam);
    }
}

void AAuracronPrismalFlow::PlaySegmentCaptureEffects(int32 SegmentIndex, int32 CapturingTeam)
{
    if (!FlowSegments.IsValidIndex(SegmentIndex))
    {
        return;
    }

    // Play segment capture effects using UE 5.6 effect system
    const FPrismalFlowSegment& Segment = FlowSegments[SegmentIndex];
    FVector SegmentCenter = (Segment.StartLocation + Segment.EndLocation) * 0.5f;

    // Play capture VFX - only if available
    static const FSoftObjectPath CaptureVFXPath(TEXT("/Game/VFX/Flow/NS_SegmentCapture.NS_SegmentCapture"));
    if (UNiagaraSystem* CaptureVFX = Cast<UNiagaraSystem>(CaptureVFXPath.TryLoad()))
    {
        UNiagaraComponent* CaptureComponent = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            CaptureVFX,
            SegmentCenter,
            FRotator::ZeroRotator,
            FVector::OneVector,
            true, // Auto-destroy
            true, // Auto-activate
            ENCPoolMethod::None
        );

        if (CaptureComponent)
        {
            CaptureComponent->SetVariableLinearColor(FName("TeamColor"), Segment.SegmentColor);
            CaptureComponent->SetVariableFloat(FName("CaptureIntensity"), 2.0f);
            CaptureComponent->SetVariableInt(FName("TeamID"), CapturingTeam);
        }

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Segment capture VFX played for segment %d"), SegmentIndex);
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Segment capture VFX not found, skipping visual effect"));
    }

    // Play capture audio
    static const FSoftObjectPath CaptureAudioPath(TEXT("/Game/Audio/Flow/MS_SegmentCapture.MS_SegmentCapture"));
    if (USoundBase* CaptureAudio = Cast<USoundBase>(CaptureAudioPath.TryLoad()))
    {
        UGameplayStatics::PlaySoundAtLocation(
            GetWorld(),
            CaptureAudio,
            SegmentCenter
        );
    }
}

void AAuracronPrismalFlow::AwardSegmentCaptureRewards(int32 TeamID, int32 SegmentIndex)
{
    if (!GetWorld())
    {
        return;
    }

    // Award segment capture rewards using UE 5.6 reward system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Awarding capture rewards to team %d for segment %d"), TeamID, SegmentIndex);

    // Find team members to reward
    for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        APlayerController* PC = Iterator->Get();
        if (PC && PC->GetPawn())
        {
            // Check if player is on the capturing team
            if (GetPlayerTeamID(PC->GetPawn()) == TeamID)
            {
                AwardPlayerCaptureReward(PC->GetPawn(), SegmentIndex);
            }
        }
    }
}

void AAuracronPrismalFlow::AwardPlayerCaptureReward(APawn* Player, int32 SegmentIndex)
{
    if (!Player)
    {
        return;
    }

    // Award player capture reward using UE 5.6 reward system
    if (UAbilitySystemComponent* ASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        FGameplayEffectContextHandle EffectContext = ASC->MakeEffectContext();
        EffectContext.AddSourceObject(this);

        // Apply capture reward effect
        static const FSoftClassPath CaptureRewardPath(TEXT("/Game/GameplayEffects/Flow/GE_SegmentCaptureReward.GE_SegmentCaptureReward_C"));
        if (TSubclassOf<UGameplayEffect> CaptureRewardClass = CaptureRewardPath.TryLoadClass<UGameplayEffect>())
        {
            if (UGameplayEffect* CaptureReward = CaptureRewardClass.GetDefaultObject())
            {
                FGameplayEffectSpec RewardSpec(CaptureReward, EffectContext, 1.0f);

            // Set reward parameters
            RewardSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Reward.Experience")), 100.0f);
            RewardSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Reward.Energy")), 50.0f);
            RewardSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Reward.SegmentBonus")), static_cast<float>(SegmentIndex + 1));

            RewardSpec.SetDuration(UGameplayEffect::INSTANT_APPLICATION, false);

            ASC->ApplyGameplayEffectSpecToSelf(RewardSpec);
            }
        }
    }
}

int32 AAuracronPrismalFlow::GetPlayerTeamID(APawn* Player) const
{
    if (!Player)
    {
        return 0;
    }

    // Get player team ID using UE 5.6 team system
    if (UAbilitySystemComponent* ASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        FGameplayTag TeamTag = FGameplayTag::RequestGameplayTag(TEXT("Player.Team"));
        if (ASC->HasMatchingGameplayTag(TeamTag))
        {
            // Extract team ID from gameplay tags
            FGameplayTagContainer PlayerTags;
            ASC->GetOwnedGameplayTags(PlayerTags);

            if (PlayerTags.HasTag(FGameplayTag::RequestGameplayTag(TEXT("Player.Team.1"))))
            {
                return 1;
            }
            else if (PlayerTags.HasTag(FGameplayTag::RequestGameplayTag(TEXT("Player.Team.2"))))
            {
                return 2;
            }
        }
    }

    return 0; // Neutral/No team
}

// === Missing Utility Methods Implementation ===

TArray<AAuracronDynamicRail*> AAuracronPrismalFlow::GetActiveRails() const
{
    // Get active rails from realm subsystem
    if (GetWorld())
    {
        if (UAuracronDynamicRealmSubsystem* RealmSubsystem = GetWorld()->GetSubsystem<UAuracronDynamicRealmSubsystem>())
        {
            return RealmSubsystem->GetActiveRails();
        }
    }

    return TArray<AAuracronDynamicRail*>();
}

// OptimizeIslandRendering duplicate implementation removed

// HandlePlayerFlowEntry duplicate implementation removed

// HandlePlayerFlowExit duplicate implementation removed






