#!/usr/bin/env python3
"""
Auracron Sigil System Creation Script
Cria o sistema de Sí<PERSON> (A<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)

Componentes:
- <PERSON><PERSON><PERSON><PERSON> (Proteção e Defesa)
- <PERSON><PERSON><PERSON><PERSON> (Destruição e Dano)
- <PERSON><PERSON><PERSON><PERSON> (Suporte e Utilidade)
- Sistema de combinação de sígilos
- Mecânicas de evolução temporal
- Sistema de descoberta procedural

Utiliza: AuracronGameplayBridge, AuracronPCGBridge
"""

import unreal
import sys
import os
import math
import random

# Configurações dos Sígilos Aegis (Proteção)
AEGIS_SIGILS = {
    'barrier_sigil': {
        'name': '<PERSON>gi<PERSON> Barreira',
        'type': 'defensive',
        'tier': 1,
        'effect': 'shield_generation',
        'power': 100,
        'duration': 15.0,
        'cooldown': 30.0,
        'mana_cost': 80,
        'visual_effect': 'blue_energy_shield',
        'audio_cue': 'aegis_barrier_cast'
    },
    'fortress_sigil': {
        'name': '<PERSON>gi<PERSON> Fortaleza',
        'type': 'defensive',
        'tier': 2,
        'effect': 'damage_reduction',
        'power': 0.30,  # 30% redução de dano
        'duration': 20.0,
        'cooldown': 45.0,
        'mana_cost': 120,
        'visual_effect': 'golden_armor_aura',
        'audio_cue': 'aegis_fortress_cast'
    },
    'sanctuary_sigil': {
        'name': 'Sigilo do Santuário',
        'type': 'defensive',
        'tier': 3,
        'effect': 'area_protection',
        'power': 200,
        'duration': 25.0,
        'cooldown': 60.0,
        'mana_cost': 150,
        'area_radius': 500.0,
        'visual_effect': 'protective_dome',
        'audio_cue': 'aegis_sanctuary_cast'
    },
    'aegis_ultimate': {
        'name': 'Égide Suprema',
        'type': 'ultimate',
        'tier': 4,
        'effect': 'invulnerability',
        'power': 1.0,  # Invulnerabilidade total
        'duration': 5.0,
        'cooldown': 120.0,
        'mana_cost': 300,
        'visual_effect': 'divine_protection',
        'audio_cue': 'aegis_ultimate_cast'
    }
}

# Configurações dos Sígilos Ruin (Destruição)
RUIN_SIGILS = {
    'flame_sigil': {
        'name': 'Sigilo das Chamas',
        'type': 'offensive',
        'tier': 1,
        'effect': 'fire_damage',
        'power': 150,
        'duration': 0.0,  # Instantâneo
        'cooldown': 8.0,
        'mana_cost': 60,
        'range': 600.0,
        'visual_effect': 'fire_projectile',
        'audio_cue': 'ruin_flame_cast'
    },
    'devastation_sigil': {
        'name': 'Sigilo da Devastação',
        'type': 'offensive',
        'tier': 2,
        'effect': 'area_damage',
        'power': 200,
        'duration': 0.0,
        'cooldown': 15.0,
        'mana_cost': 100,
        'range': 400.0,
        'area_radius': 300.0,
        'visual_effect': 'explosion_blast',
        'audio_cue': 'ruin_devastation_cast'
    },
    'annihilation_sigil': {
        'name': 'Sigilo da Aniquilação',
        'type': 'offensive',
        'tier': 3,
        'effect': 'piercing_damage',
        'power': 300,
        'duration': 0.0,
        'cooldown': 25.0,
        'mana_cost': 140,
        'range': 800.0,
        'pierce_count': 5,
        'visual_effect': 'dark_beam',
        'audio_cue': 'ruin_annihilation_cast'
    },
    'ruin_ultimate': {
        'name': 'Ruína Absoluta',
        'type': 'ultimate',
        'tier': 4,
        'effect': 'massive_area_damage',
        'power': 500,
        'duration': 3.0,
        'cooldown': 100.0,
        'mana_cost': 250,
        'range': 1000.0,
        'area_radius': 600.0,
        'visual_effect': 'apocalyptic_storm',
        'audio_cue': 'ruin_ultimate_cast'
    }
}

# Configurações dos Sígilos Vesper (Suporte)
VESPER_SIGILS = {
    'healing_sigil': {
        'name': 'Sigilo da Cura',
        'type': 'support',
        'tier': 1,
        'effect': 'health_restoration',
        'power': 120,
        'duration': 0.0,
        'cooldown': 12.0,
        'mana_cost': 70,
        'range': 500.0,
        'visual_effect': 'green_healing_light',
        'audio_cue': 'vesper_healing_cast'
    },
    'enhancement_sigil': {
        'name': 'Sigilo do Aprimoramento',
        'type': 'support',
        'tier': 2,
        'effect': 'stat_boost',
        'power': 0.25,  # 25% aumento de stats
        'duration': 30.0,
        'cooldown': 40.0,
        'mana_cost': 90,
        'range': 400.0,
        'visual_effect': 'empowerment_aura',
        'audio_cue': 'vesper_enhancement_cast'
    },
    'restoration_sigil': {
        'name': 'Sigilo da Restauração',
        'type': 'support',
        'tier': 3,
        'effect': 'full_recovery',
        'power': 1.0,  # Recuperação completa
        'duration': 0.0,
        'cooldown': 80.0,
        'mana_cost': 180,
        'range': 600.0,
        'visual_effect': 'divine_restoration',
        'audio_cue': 'vesper_restoration_cast'
    },
    'vesper_ultimate': {
        'name': 'Bênção Vespertina',
        'type': 'ultimate',
        'tier': 4,
        'effect': 'team_empowerment',
        'power': 0.50,  # 50% boost para toda equipe
        'duration': 45.0,
        'cooldown': 150.0,
        'mana_cost': 280,
        'area_radius': 1000.0,
        'visual_effect': 'celestial_blessing',
        'audio_cue': 'vesper_ultimate_cast'
    }
}

# Sistema de Combinações de Sígilos
SIGIL_COMBINATIONS = {
    'aegis_ruin': {
        'name': 'Escudo Explosivo',
        'components': ['barrier_sigil', 'flame_sigil'],
        'effect': 'defensive_counter',
        'description': 'Escudo que explode ao ser quebrado',
        'power_multiplier': 1.5,
        'cooldown_reduction': 0.2
    },
    'ruin_vesper': {
        'name': 'Chama Curativa',
        'components': ['flame_sigil', 'healing_sigil'],
        'effect': 'damage_and_heal',
        'description': 'Causa dano aos inimigos e cura aliados',
        'power_multiplier': 1.3,
        'cooldown_reduction': 0.15
    },
    'vesper_aegis': {
        'name': 'Proteção Regenerativa',
        'components': ['healing_sigil', 'barrier_sigil'],
        'effect': 'regenerating_shield',
        'description': 'Escudo que se regenera ao longo do tempo',
        'power_multiplier': 1.4,
        'cooldown_reduction': 0.25
    },
    'trinity_combination': {
        'name': 'Tríade Auracron',
        'components': ['fortress_sigil', 'devastation_sigil', 'enhancement_sigil'],
        'effect': 'ultimate_synergy',
        'description': 'Combinação suprema dos três aspectos',
        'power_multiplier': 2.0,
        'cooldown_reduction': 0.3
    }
}

# Configurações de Descoberta Procedural
DISCOVERY_CONFIG = {
    'sigil_spawn_locations': {
        'ancient_ruins': 15,      # Locais com ruínas antigas
        'energy_convergences': 8, # Pontos de convergência energética
        'hidden_chambers': 12,    # Câmaras secretas
        'boss_lairs': 5,         # Covis de chefes
        'random_world': 20       # Locais aleatórios pelo mundo
    },
    'discovery_mechanics': {
        'exploration_radius': 100.0,  # Raio para descobrir sígilos
        'discovery_chance': 0.15,     # 15% chance por área explorada
        'rare_sigil_chance': 0.05,    # 5% chance de sígilos raros
        'combination_unlock_chance': 0.08  # 8% chance de desbloquear combinações
    },
    'progression_system': {
        'sigils_per_tier': {
            'tier_1': 3,  # Sígilos básicos por categoria
            'tier_2': 2,  # Sígilos intermediários
            'tier_3': 1,  # Sígilos avançados
            'tier_4': 1   # Sígilos supremos
        },
        'unlock_requirements': {
            'tier_2': {'player_level': 10, 'sigils_discovered': 5},
            'tier_3': {'player_level': 20, 'sigils_discovered': 12},
            'tier_4': {'player_level': 30, 'sigils_discovered': 20}
        }
    }
}

class SigilSystemCreator:
    def __init__(self):
        self.world = unreal.EditorLevelLibrary.get_editor_world()
        self.asset_tools = unreal.AssetToolsHelpers.get_asset_tools()
        self.created_sigils = []
        self.sigil_locations = []
        self.combination_altars = []
        
    def create_sigil_system(self):
        """Cria o sistema completo de sígilos"""
        print("Criando sistema de Sígilos Auracron...")
        
        # Criar sígilos Aegis
        self._create_aegis_sigils()
        
        # Criar sígilos Ruin
        self._create_ruin_sigils()
        
        # Criar sígilos Vesper
        self._create_vesper_sigils()
        
        # Criar sistema de combinações
        self._create_combination_system()
        
        # Criar sistema de descoberta
        self._create_discovery_system()
        
        # Configurar evolução temporal
        self._setup_temporal_evolution()
        
        print(f"✓ Sistema criado: {len(self.created_sigils)} sígilos, {len(self.combination_altars)} altares")
        
    def _create_aegis_sigils(self):
        """Cria todos os sígilos Aegis"""
        print("Criando sígilos Aegis...")
        
        for sigil_id, sigil_config in AEGIS_SIGILS.items():
            sigil_actor = self._create_sigil_actor(sigil_id, sigil_config, 'Aegis')
            if sigil_actor:
                self.created_sigils.append(sigil_actor)
                
    def _create_ruin_sigils(self):
        """Cria todos os sígilos Ruin"""
        print("Criando sígilos Ruin...")
        
        for sigil_id, sigil_config in RUIN_SIGILS.items():
            sigil_actor = self._create_sigil_actor(sigil_id, sigil_config, 'Ruin')
            if sigil_actor:
                self.created_sigils.append(sigil_actor)
                
    def _create_vesper_sigils(self):
        """Cria todos os sígilos Vesper"""
        print("Criando sígilos Vesper...")
        
        for sigil_id, sigil_config in VESPER_SIGILS.items():
            sigil_actor = self._create_sigil_actor(sigil_id, sigil_config, 'Vesper')
            if sigil_actor:
                self.created_sigils.append(sigil_actor)
                
    def _create_sigil_actor(self, sigil_id, config, category):
        """Cria um ator de sigilo individual"""
        sigil_name = f"Sigil_{category}_{sigil_id}"
        
        # Criar ator do sigilo
        sigil_actor = unreal.EditorLevelLibrary.spawn_actor_from_class(
            unreal.Actor,
            unreal.Vector(0, 0, 5000)  # Posição temporária
        )
        
        if sigil_actor:
            sigil_actor.set_actor_label(sigil_name)
            
            # Adicionar componentes do sigilo
            components = self._add_sigil_components(sigil_actor, config)
            
            # Configurar propriedades do sigilo
            self._configure_sigil_properties(sigil_actor, config, components)
            
            # Adicionar sistema de ativação
            self._add_sigil_activation_system(sigil_actor, config)
            
            return sigil_actor
            
        return None
        
    def _add_sigil_components(self, actor, config):
        """Adiciona componentes necessários ao sigilo"""
        components = {}
        
        # Componente visual principal
        components['main_mesh'] = actor.add_component_by_class(unreal.StaticMeshComponent)
        
        # Componente de efeitos visuais
        components['visual_effect'] = actor.add_component_by_class(unreal.NiagaraComponent)
        
        # Componente de áudio
        components['audio'] = actor.add_component_by_class(unreal.AudioComponent)
        
        # Componente de colisão para ativação
        components['activation_trigger'] = actor.add_component_by_class(unreal.SphereComponent)
        
        # Componente de área de efeito (se aplicável)
        if 'area_radius' in config:
            components['effect_area'] = actor.add_component_by_class(unreal.SphereComponent)
            
        # Componente de projétil (para sígilos ofensivos)
        if config['type'] == 'offensive':
            components['projectile'] = actor.add_component_by_class(unreal.ProjectileMovementComponent)
            
        return components
        
    def _configure_sigil_properties(self, actor, config, components):
        """Configura propriedades específicas do sigilo"""
        # Configurar trigger de ativação
        if components['activation_trigger']:
            components['activation_trigger'].set_sphere_radius(150.0)
            
        # Configurar área de efeito
        if 'effect_area' in components and 'area_radius' in config:
            components['effect_area'].set_sphere_radius(config['area_radius'])
            
        # Configurar efeitos visuais baseados no tipo
        self._configure_sigil_visual_effects(components, config)
        
        # Configurar propriedades de gameplay
        self._configure_sigil_gameplay_properties(actor, config)
        
    def _configure_sigil_visual_effects(self, components, config):
        """Configura efeitos visuais do sigilo"""
        if components['visual_effect']:
            # Configurar efeito baseado no tipo de sigilo
            effect_intensity = {
                'defensive': 0.8,
                'offensive': 1.2,
                'support': 0.9,
                'ultimate': 1.5
            }.get(config['type'], 1.0)
            
            # Aplicar intensidade e cor baseada na categoria
            # components['visual_effect'].set_float_parameter("Intensity", effect_intensity)
            
    def _configure_sigil_gameplay_properties(self, actor, config):
        """Configura propriedades de gameplay do sigilo"""
        # Adicionar componente de gameplay customizado
        gameplay_component = actor.add_component_by_class(unreal.ActorComponent)
        
        # Configurar propriedades do sigilo
        # gameplay_component.set_sigil_properties(config)
        
    def _add_sigil_activation_system(self, actor, config):
        """Adiciona sistema de ativação ao sigilo"""
        # Componente de ativação que gerencia:
        # - Condições de ativação
        # - Cooldowns
        # - Custos de mana
        # - Efeitos aplicados
        activation_component = actor.add_component_by_class(unreal.ActorComponent)
        
        # Configurar sistema de ativação
        # activation_component.configure_activation(config)
        
    def _create_combination_system(self):
        """Cria sistema de combinação de sígilos"""
        print("Criando sistema de combinações...")
        
        # Criar altares de combinação
        self._create_combination_altars()
        
        # Configurar receitas de combinação
        self._setup_combination_recipes()
        
        # Criar sistema de descoberta de combinações
        self._setup_combination_discovery()
        
    def _create_combination_altars(self):
        """Cria altares para combinação de sígilos"""
        # Posições estratégicas para altares
        altar_positions = [
            (0, 0, 100),        # Centro do mapa
            (2000, 2000, 150),  # Nordeste
            (-2000, 2000, 120), # Noroeste
            (2000, -2000, 180), # Sudeste
            (-2000, -2000, 140) # Sudoeste
        ]
        
        for i, position in enumerate(altar_positions):
            altar = self._create_combination_altar(position, i)
            if altar:
                self.combination_altars.append(altar)
                
    def _create_combination_altar(self, position, index):
        """Cria um altar de combinação individual"""
        altar_name = f"CombinationAltar_{index}"
        
        # Criar ator do altar
        altar_actor = unreal.EditorLevelLibrary.spawn_actor_from_class(
            unreal.Actor,
            unreal.Vector(position[0], position[1], position[2])
        )
        
        if altar_actor:
            altar_actor.set_actor_label(altar_name)
            
            # Adicionar componentes do altar
            self._add_altar_components(altar_actor)
            
            return altar_actor
            
        return None
        
    def _add_altar_components(self, actor):
        """Adiciona componentes ao altar de combinação"""
        # Mesh principal do altar
        main_mesh = actor.add_component_by_class(unreal.StaticMeshComponent)
        
        # Efeito visual de energia
        energy_effect = actor.add_component_by_class(unreal.NiagaraComponent)
        
        # Área de interação
        interaction_area = actor.add_component_by_class(unreal.SphereComponent)
        interaction_area.set_sphere_radius(300.0)
        
        # Slots para sígilos (3 slots para combinações)
        for i in range(3):
            slot_component = actor.add_component_by_class(unreal.StaticMeshComponent)
            # Posicionar slots em círculo ao redor do altar
            
        # Áudio ambiente
        ambient_audio = actor.add_component_by_class(unreal.AudioComponent)
        
    def _setup_combination_recipes(self):
        """Configura receitas de combinação"""
        # Registrar todas as combinações possíveis
        for combo_id, combo_config in SIGIL_COMBINATIONS.items():
            self._register_combination_recipe(combo_id, combo_config)
            
    def _register_combination_recipe(self, combo_id, config):
        """Registra uma receita de combinação"""
        # Criar dados da receita para o sistema de gameplay
        # Isso será usado pelos altares para validar combinações
        pass
        
    def _setup_combination_discovery(self):
        """Configura sistema de descoberta de combinações"""
        # Sistema que permite aos jogadores descobrir novas combinações
        # através de experimentação
        pass
        
    def _create_discovery_system(self):
        """Cria sistema de descoberta procedural"""
        print("Criando sistema de descoberta...")
        
        # Criar locais de descoberta
        self._create_discovery_locations()
        
        # Configurar mecânicas de descoberta
        self._setup_discovery_mechanics()
        
        # Criar sistema de progressão
        self._setup_progression_system()
        
    def _create_discovery_locations(self):
        """Cria locais onde sígilos podem ser descobertos"""
        # Ruínas antigas
        self._create_ancient_ruins()
        
        # Câmaras secretas
        self._create_hidden_chambers()
        
        # Locais de convergência energética
        self._create_energy_convergence_sites()
        
        # Covis de chefes
        self._create_boss_lairs()
        
        # Locais aleatórios
        self._create_random_discovery_sites()
        
    def _create_ancient_ruins(self):
        """Cria ruínas antigas com sígilos"""
        ruin_count = DISCOVERY_CONFIG['sigil_spawn_locations']['ancient_ruins']
        
        for i in range(ruin_count):
            position = self._generate_random_world_position()
            ruin = self._create_discovery_site(position, 'ancient_ruin', i)
            if ruin:
                self.sigil_locations.append(ruin)
                
    def _create_hidden_chambers(self):
        """Cria câmaras secretas com sígilos"""
        chamber_count = DISCOVERY_CONFIG['sigil_spawn_locations']['hidden_chambers']
        
        for i in range(chamber_count):
            position = self._generate_random_world_position()
            chamber = self._create_discovery_site(position, 'hidden_chamber', i)
            if chamber:
                self.sigil_locations.append(chamber)
                
    def _create_energy_convergence_sites(self):
        """Cria locais de convergência energética"""
        convergence_count = DISCOVERY_CONFIG['sigil_spawn_locations']['energy_convergences']
        
        for i in range(convergence_count):
            position = self._generate_random_world_position()
            site = self._create_discovery_site(position, 'energy_convergence', i)
            if site:
                self.sigil_locations.append(site)
                
    def _create_boss_lairs(self):
        """Cria covis de chefes com sígilos raros"""
        lair_count = DISCOVERY_CONFIG['sigil_spawn_locations']['boss_lairs']
        
        for i in range(lair_count):
            position = self._generate_random_world_position()
            lair = self._create_discovery_site(position, 'boss_lair', i)
            if lair:
                self.sigil_locations.append(lair)
                
    def _create_random_discovery_sites(self):
        """Cria locais aleatórios de descoberta"""
        random_count = DISCOVERY_CONFIG['sigil_spawn_locations']['random_world']
        
        for i in range(random_count):
            position = self._generate_random_world_position()
            site = self._create_discovery_site(position, 'random_site', i)
            if site:
                self.sigil_locations.append(site)
                
    def _generate_random_world_position(self):
        """Gera uma posição aleatória no mundo"""
        # Gerar posição dentro dos limites do mapa
        x = random.uniform(-5000, 5000)
        y = random.uniform(-5000, 5000)
        z = random.uniform(0, 1000)
        
        return (x, y, z)
        
    def _create_discovery_site(self, position, site_type, index):
        """Cria um local de descoberta"""
        site_name = f"DiscoverySite_{site_type}_{index}"
        
        # Criar ator do local
        site_actor = unreal.EditorLevelLibrary.spawn_actor_from_class(
            unreal.Actor,
            unreal.Vector(position[0], position[1], position[2])
        )
        
        if site_actor:
            site_actor.set_actor_label(site_name)
            
            # Adicionar componentes do local
            self._add_discovery_site_components(site_actor, site_type)
            
            return site_actor
            
        return None
        
    def _add_discovery_site_components(self, actor, site_type):
        """Adiciona componentes ao local de descoberta"""
        # Mesh do local baseado no tipo
        main_mesh = actor.add_component_by_class(unreal.StaticMeshComponent)
        
        # Efeito visual de descoberta
        discovery_effect = actor.add_component_by_class(unreal.NiagaraComponent)
        
        # Área de descoberta
        discovery_area = actor.add_component_by_class(unreal.SphereComponent)
        discovery_area.set_sphere_radius(DISCOVERY_CONFIG['discovery_mechanics']['exploration_radius'])
        
        # Componente de sigilo oculto
        hidden_sigil = actor.add_component_by_class(unreal.ActorComponent)
        
        # Áudio ambiente
        ambient_audio = actor.add_component_by_class(unreal.AudioComponent)
        
    def _setup_discovery_mechanics(self):
        """Configura mecânicas de descoberta"""
        # Sistema de detecção de proximidade
        # Chances de descoberta baseadas em exploração
        # Sistema de pistas visuais
        pass
        
    def _setup_progression_system(self):
        """Configura sistema de progressão"""
        # Criar gerenciador de progressão
        progression_manager = self._create_progression_manager()
        
        # Configurar requisitos de desbloqueio
        self._setup_unlock_requirements()
        
        # Configurar recompensas de descoberta
        self._setup_discovery_rewards()
        
    def _create_progression_manager(self):
        """Cria gerenciador de progressão"""
        manager_actor = unreal.EditorLevelLibrary.spawn_actor_from_class(
            unreal.Actor,
            unreal.Vector(0, 0, 6000)
        )
        
        if manager_actor:
            manager_actor.set_actor_label("SigilProgressionManager")
            
            # Adicionar componente de gerenciamento
            progression_component = manager_actor.add_component_by_class(unreal.ActorComponent)
            
        return manager_actor
        
    def _setup_unlock_requirements(self):
        """Configura requisitos para desbloquear sígilos"""
        unlock_config = DISCOVERY_CONFIG['progression_system']['unlock_requirements']
        
        # Configurar requisitos por tier
        for tier, requirements in unlock_config.items():
            self._register_tier_requirements(tier, requirements)
            
    def _register_tier_requirements(self, tier, requirements):
        """Registra requisitos para um tier específico"""
        # Registrar no sistema de progressão
        pass
        
    def _setup_discovery_rewards(self):
        """Configura recompensas por descoberta"""
        # Experiência por descoberta
        # Moeda especial
        # Desbloqueio de novas áreas
        pass
        
    def _setup_temporal_evolution(self):
        """Configura evolução temporal dos sígilos"""
        print("Configurando evolução temporal...")
        
        # Criar controlador de evolução
        evolution_controller = self._create_evolution_controller()
        
        # Configurar fases de evolução
        self._setup_evolution_phases()
        
        # Configurar mudanças temporais
        self._setup_temporal_changes()
        
    def _create_evolution_controller(self):
        """Cria controlador de evolução temporal"""
        controller_actor = unreal.EditorLevelLibrary.spawn_actor_from_class(
            unreal.Actor,
            unreal.Vector(0, 0, 7000)
        )
        
        if controller_actor:
            controller_actor.set_actor_label("SigilEvolutionController")
            
            # Adicionar componente de evolução
            evolution_component = controller_actor.add_component_by_class(unreal.ActorComponent)
            
        return controller_actor
        
    def _setup_evolution_phases(self):
        """Configura fases de evolução"""
        # Fase inicial (sígilos básicos)
        # Fase intermediária (sígilos aprimorados)
        # Fase avançada (sígilos supremos)
        # Fase final (sígilos transcendentes)
        pass
        
    def _setup_temporal_changes(self):
        """Configura mudanças temporais"""
        # Mudanças baseadas no tempo de jogo
        # Eventos especiais que afetam sígilos
        # Ciclos de poder dos sígilos
        pass
        
    def validate_sigil_system(self):
        """Valida se o sistema foi criado corretamente"""
        print("Validando sistema de sígilos...")
        
        validation_results = {
            'sigils_created': len(self.created_sigils) > 0,
            'altars_created': len(self.combination_altars) > 0,
            'discovery_sites': len(self.sigil_locations) > 0,
            'aegis_sigils': any('Aegis' in s.get_actor_label() for s in self.created_sigils),
            'ruin_sigils': any('Ruin' in s.get_actor_label() for s in self.created_sigils),
            'vesper_sigils': any('Vesper' in s.get_actor_label() for s in self.created_sigils)
        }
        
        all_valid = all(validation_results.values())
        
        if all_valid:
            print("✓ Sistema de Sígilos configurado corretamente")
            print(f"  - {len(self.created_sigils)} sígilos criados")
            print(f"  - {len(self.combination_altars)} altares de combinação")
            print(f"  - {len(self.sigil_locations)} locais de descoberta")
        else:
            print("✗ Problemas encontrados no sistema")
            
        return all_valid

def main():
    """Função principal"""
    print("=== Auracron Sigil System Creator ===")
    
    creator = SigilSystemCreator()
    
    try:
        # Criar sistema completo
        creator.create_sigil_system()
        
        # Validar criação
        if creator.validate_sigil_system():
            print("✓ Sistema de Sígilos criado com sucesso!")
            return True
        else:
            print("✗ Falha na validação do sistema")
            return False
            
    except Exception as e:
        print(f"✗ Erro durante criação: {str(e)}")
        return False

if __name__ == "__main__":
    main()