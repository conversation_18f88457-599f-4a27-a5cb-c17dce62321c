# Script para corrigir TODOS os módulos sem IMPLEMENT_MODULE
$ModulesWithoutImplement = @(
    "Auracron",
    "AuracronAdaptiveCreaturesBridge",
    "AuracronAdaptiveEngagementBridge", 
    "AuracronAnalyticsBridge",
    "AuracronAudioBridge",
    "AuracronChampionsBridge",
    "AuracronCombatBridge",
    "AuracronEOSBridge",
    "AuracronIntelligentDocumentationBridge",
    "AuracronLoreBridge",
    "AuracronMasterOrchestrator",
    "AuracronMonetizationBridge",
    "AuracronNaniteBridge",
    "AuracronNetworkingBridge",
    "AuracronPhysicsBridge",
    "AuracronProgressionBridge",
    "AuracronQABridge",
    "AuracronQuantumConsciousnessBridge",
    "AuracronRealmsBridge",
    "AuracronSigilosBridge",
    "AuracronTutorialBridge",
    "AuracronUIBridge",
    "AuracronVerticalTransitionsBridge",
    "AuracronVoiceBridge"
)

foreach ($ModuleName in $ModulesWithoutImplement) {
    Write-Host "Corrigindo módulo: $ModuleName" -ForegroundColor Yellow
    
    $ModulePath = "Source\$ModuleName"
    if (Test-Path $ModulePath) {
        # Buscar arquivo .cpp principal
        $CppFiles = Get-ChildItem -Path $ModulePath -Recurse -Filter "*.cpp"
        $MainCppFile = $null
        
        # Tentar encontrar arquivo principal do módulo
        foreach ($file in $CppFiles) {
            if ($file.Name -like "*$ModuleName*.cpp" -or $file.Name -like "*Module*.cpp") {
                $MainCppFile = $file
                break
            }
        }
        
        # Se não encontrou, usar o primeiro arquivo .cpp
        if (-not $MainCppFile -and $CppFiles.Count -gt 0) {
            $MainCppFile = $CppFiles[0]
        }
        
        if ($MainCppFile) {
            $Content = Get-Content $MainCppFile.FullName -Raw
            
            # Verificar se já tem IMPLEMENT_MODULE
            if ($Content -notmatch "IMPLEMENT_MODULE") {
                Write-Host "  Adicionando IMPLEMENT_MODULE em: $($MainCppFile.FullName)" -ForegroundColor Green
                
                # Adicionar includes e IMPLEMENT_MODULE no início do arquivo
                $NewContent = $Content -replace '(#include\s+"[^"]*\.h"[^\r\n]*)', "`$1`r`n#include `"Modules/ModuleManager.h`"`r`n`r`nIMPLEMENT_MODULE(FDefaultModuleImpl, $ModuleName);"
                
                # Se não encontrou include para substituir, adicionar no início
                if ($NewContent -eq $Content) {
                    $NewContent = "#include `"Modules/ModuleManager.h`"`r`n`r`nIMPLEMENT_MODULE(FDefaultModuleImpl, $ModuleName);`r`n`r`n" + $Content
                }
                
                Set-Content -Path $MainCppFile.FullName -Value $NewContent -Encoding UTF8
                Write-Host "  ✅ IMPLEMENT_MODULE adicionado com sucesso!" -ForegroundColor Green
            } else {
                Write-Host "  ⚠️ Módulo já tem IMPLEMENT_MODULE" -ForegroundColor Yellow
            }
        } else {
            Write-Host "  ❌ Nenhum arquivo .cpp encontrado em $ModulePath" -ForegroundColor Red
        }
    } else {
        Write-Host "  ❌ Caminho do módulo não encontrado: $ModulePath" -ForegroundColor Red
    }
}

Write-Host "`n🎉 Correção de todos os módulos concluída!" -ForegroundColor Cyan
