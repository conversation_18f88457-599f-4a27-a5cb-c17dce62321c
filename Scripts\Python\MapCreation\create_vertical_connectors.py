#!/usr/bin/env python3
"""
Auracron Vertical Connectors Creation Script
Cria portais, fendas e elevadores para conectar as três camadas verticalmente

Tipos de conectores:
- Portais Prismáticos (teleporte instantâneo)
- <PERSON><PERSON> (transição gradual)
- Elevadores Cristalinos (movimento físico)

Utiliza: AuracronDynamicRealmBridge
"""

import unreal
import sys
import os
import math

# Configurações dos conectores verticais
CONNECTOR_CONFIGS = {
    'prismatic_portals': {
        'count': 6,
        'radius': 5000,  # Distância do centro do mapa
        'connections': [
            ('terrestrial', 'celestial'),
            ('terrestrial', 'abyssal'),
            ('celestial', 'abyssal')
        ],
        'activation_time': 2.0,
        'cooldown_time': 5.0
    },
    'temporal_rifts': {
        'count': 4,
        'radius': 3000,
        'connections': [
            ('terrestrial', 'celestial'),
            ('celestial', 'abyssal')
        ],
        'transition_time': 3.0,
        'vulnerability_window': 1.5
    },
    'crystal_elevators': {
        'count': 8,
        'radius': 2000,
        'connections': [
            ('terrestrial', 'celestial'),
            ('terrestrial', 'abyssal')
        ],
        'travel_speed': 500.0,
        'capacity': 5
    }
}

# Posições estratégicas para conectores
STRATEGIC_POSITIONS = {
    'jungle_entrances': [(2000, 2000), (-2000, -2000), (2000, -2000), (-2000, 2000)],
    'lane_intersections': [(1500, 0), (-1500, 0), (0, 1500), (0, -1500)],
    'objective_areas': [(3000, 1000), (-3000, -1000), (1000, 3000), (-1000, -3000)]
}

class VerticalConnectorsCreator:
    def __init__(self):
        self.world = unreal.EditorLevelLibrary.get_editor_world()
        self.asset_tools = unreal.AssetToolsHelpers.get_asset_tools()
        self.created_connectors = []
        
    def create_all_connectors(self):
        """Cria todos os tipos de conectores verticais"""
        print("Criando conectores verticais...")
        
        # Criar portais prismáticos
        self._create_prismatic_portals()
        
        # Criar fendas temporais
        self._create_temporal_rifts()
        
        # Criar elevadores cristalinos
        self._create_crystal_elevators()
        
        print(f"✓ {len(self.created_connectors)} conectores criados")
        
    def _create_prismatic_portals(self):
        """Cria portais prismáticos para teleporte instantâneo"""
        print("Criando portais prismáticos...")
        
        config = CONNECTOR_CONFIGS['prismatic_portals']
        positions = self._generate_circular_positions(config['count'], config['radius'])
        
        for i, pos in enumerate(positions):
            for connection in config['connections']:
                portal = self._create_portal(pos, connection, i)
                if portal:
                    self.created_connectors.append(portal)
                    
    def _create_temporal_rifts(self):
        """Cria fendas temporais para transição gradual"""
        print("Criando fendas temporais...")
        
        config = CONNECTOR_CONFIGS['temporal_rifts']
        positions = STRATEGIC_POSITIONS['jungle_entrances']
        
        for i, pos in enumerate(positions):
            for connection in config['connections']:
                rift = self._create_rift(pos, connection, i)
                if rift:
                    self.created_connectors.append(rift)
                    
    def _create_crystal_elevators(self):
        """Cria elevadores cristalinos para movimento físico"""
        print("Criando elevadores cristalinos...")
        
        config = CONNECTOR_CONFIGS['crystal_elevators']
        positions = STRATEGIC_POSITIONS['lane_intersections'] + STRATEGIC_POSITIONS['objective_areas']
        
        for i, pos in enumerate(positions):
            for connection in config['connections']:
                elevator = self._create_elevator(pos, connection, i)
                if elevator:
                    self.created_connectors.append(elevator)
                    
    def _generate_circular_positions(self, count, radius):
        """Gera posições em círculo"""
        positions = []
        angle_step = 2 * math.pi / count
        
        for i in range(count):
            angle = i * angle_step
            x = radius * math.cos(angle)
            y = radius * math.sin(angle)
            positions.append((x, y))
            
        return positions
        
    def _create_portal(self, position, connection, index):
        """Cria um portal prismático"""
        portal_name = f"PrismaticPortal_{connection[0]}_{connection[1]}_{index}"
        
        # Criar actor do portal
        portal_actor = unreal.EditorLevelLibrary.spawn_actor_from_class(
            unreal.Actor,
            unreal.Vector(position[0], position[1], 0)
        )
        
        if portal_actor:
            portal_actor.set_actor_label(portal_name)
            
            # Adicionar componentes do portal
            self._add_portal_components(portal_actor, connection)
            
            # Configurar propriedades do portal
            self._configure_portal_properties(portal_actor, CONNECTOR_CONFIGS['prismatic_portals'])
            
            return portal_actor
            
        return None
        
    def _create_rift(self, position, connection, index):
        """Cria uma fenda temporal"""
        rift_name = f"TemporalRift_{connection[0]}_{connection[1]}_{index}"
        
        # Criar actor da fenda
        rift_actor = unreal.EditorLevelLibrary.spawn_actor_from_class(
            unreal.Actor,
            unreal.Vector(position[0], position[1], 0)
        )
        
        if rift_actor:
            rift_actor.set_actor_label(rift_name)
            
            # Adicionar componentes da fenda
            self._add_rift_components(rift_actor, connection)
            
            # Configurar propriedades da fenda
            self._configure_rift_properties(rift_actor, CONNECTOR_CONFIGS['temporal_rifts'])
            
            return rift_actor
            
        return None
        
    def _create_elevator(self, position, connection, index):
        """Cria um elevador cristalino"""
        elevator_name = f"CrystalElevator_{connection[0]}_{connection[1]}_{index}"
        
        # Criar actor do elevador
        elevator_actor = unreal.EditorLevelLibrary.spawn_actor_from_class(
            unreal.Actor,
            unreal.Vector(position[0], position[1], 0)
        )
        
        if elevator_actor:
            elevator_actor.set_actor_label(elevator_name)
            
            # Adicionar componentes do elevador
            self._add_elevator_components(elevator_actor, connection)
            
            # Configurar propriedades do elevador
            self._configure_elevator_properties(elevator_actor, CONNECTOR_CONFIGS['crystal_elevators'])
            
            return elevator_actor
            
        return None
        
    def _add_portal_components(self, actor, connection):
        """Adiciona componentes específicos do portal"""
        # Adicionar Static Mesh Component
        mesh_comp = actor.add_component_by_class(unreal.StaticMeshComponent)
        
        # Adicionar Particle System Component para efeitos visuais
        particle_comp = actor.add_component_by_class(unreal.ParticleSystemComponent)
        
        # Adicionar Audio Component para efeitos sonoros
        audio_comp = actor.add_component_by_class(unreal.AudioComponent)
        
        # Adicionar Box Collision para detecção de jogadores
        collision_comp = actor.add_component_by_class(unreal.BoxComponent)
        
    def _add_rift_components(self, actor, connection):
        """Adiciona componentes específicos da fenda"""
        # Adicionar Static Mesh Component
        mesh_comp = actor.add_component_by_class(unreal.StaticMeshComponent)
        
        # Adicionar Niagara Component para efeitos avançados
        niagara_comp = actor.add_component_by_class(unreal.NiagaraComponent)
        
        # Adicionar Sphere Collision para área de efeito
        collision_comp = actor.add_component_by_class(unreal.SphereComponent)
        
    def _add_elevator_components(self, actor, connection):
        """Adiciona componentes específicos do elevador"""
        # Adicionar Static Mesh Component para plataforma
        platform_comp = actor.add_component_by_class(unreal.StaticMeshComponent)
        
        # Adicionar Movement Component para animação
        movement_comp = actor.add_component_by_class(unreal.InterpToMovementComponent)
        
        # Adicionar Box Collision para plataforma
        collision_comp = actor.add_component_by_class(unreal.BoxComponent)
        
    def _configure_portal_properties(self, actor, config):
        """Configura propriedades específicas do portal"""
        # Configurar tempo de ativação
        # Configurar cooldown
        # Configurar efeitos visuais
        pass
        
    def _configure_rift_properties(self, actor, config):
        """Configura propriedades específicas da fenda"""
        # Configurar tempo de transição
        # Configurar janela de vulnerabilidade
        # Configurar efeitos temporais
        pass
        
    def _configure_elevator_properties(self, actor, config):
        """Configura propriedades específicas do elevador"""
        # Configurar velocidade de viagem
        # Configurar capacidade
        # Configurar pontos de parada
        pass
        
    def setup_connector_network(self):
        """Configura rede de conectores e suas interações"""
        print("Configurando rede de conectores...")
        
        # Configurar sistema de cooldown global
        # Configurar balanceamento de uso
        # Configurar eventos de ativação
        pass
        
    def create_connector_ui_elements(self):
        """Cria elementos de UI para os conectores"""
        print("Criando elementos de UI...")
        
        # Criar indicadores visuais
        # Criar tooltips informativos
        # Criar sistema de preview de destino
        pass
        
    def validate_connector_placement(self):
        """Valida se os conectores foram posicionados corretamente"""
        print("Validando posicionamento dos conectores...")
        
        validation_results = {
            'portals_created': len([c for c in self.created_connectors if 'Portal' in c.get_actor_label()]) > 0,
            'rifts_created': len([c for c in self.created_connectors if 'Rift' in c.get_actor_label()]) > 0,
            'elevators_created': len([c for c in self.created_connectors if 'Elevator' in c.get_actor_label()]) > 0,
            'strategic_coverage': True  # Verificar se cobrem áreas estratégicas
        }
        
        all_valid = all(validation_results.values())
        
        if all_valid:
            print("✓ Conectores posicionados corretamente")
        else:
            print("✗ Problemas no posicionamento dos conectores")
            
        return all_valid

def main():
    """Função principal"""
    print("=== Auracron Vertical Connectors Creator ===")
    
    creator = VerticalConnectorsCreator()
    
    try:
        # Criar todos os conectores
        creator.create_all_connectors()
        
        # Configurar rede de conectores
        creator.setup_connector_network()
        
        # Criar elementos de UI
        creator.create_connector_ui_elements()
        
        # Validar criação
        if creator.validate_connector_placement():
            print("✓ Conectores verticais criados com sucesso!")
            return True
        else:
            print("✗ Falha na validação dos conectores")
            return False
            
    except Exception as e:
        print(f"✗ Erro durante criação: {str(e)}")
        return False

if __name__ == "__main__":
    main()