# Script para limpar IMPLEMENT_MODULE duplicados
$ModulesWithProblems = @(
    "AuracronNetworkingBridge",
    "AuracronSigilosBridge", 
    "AuracronLoreBridge"
)

foreach ($ModuleName in $ModulesWithProblems) {
    Write-Host "Limpando módulo: $ModuleName" -ForegroundColor Yellow
    
    $ModulePath = "Source\$ModuleName"
    if (Test-Path $ModulePath) {
        # Buscar arquivo .cpp principal
        $CppFiles = Get-ChildItem -Path $ModulePath -Recurse -Filter "*.cpp"
        $MainCppFile = $null
        
        # Tentar encontrar arquivo principal do módulo
        foreach ($file in $CppFiles) {
            if ($file.Name -like "*$ModuleName*.cpp" -or $file.Name -like "*Module*.cpp") {
                $MainCppFile = $file
                break
            }
        }
        
        # Se não encontrou, usar o primeiro arquivo .cpp
        if (-not $MainCppFile -and $CppFiles.Count -gt 0) {
            $MainCppFile = $CppFiles[0]
        }
        
        if ($MainCppFile) {
            Write-Host "  Limpando: $($MainCppFile.FullName)" -ForegroundColor Green
            
            $Content = Get-Content $MainCppFile.FullName -Raw
            
            # Remover todas as linhas IMPLEMENT_MODULE
            $CleanContent = $Content -replace 'IMPLEMENT_MODULE\(FDefaultModuleImpl, [^)]+\);\r?\n?', ''
            
            # Remover linhas vazias extras
            $CleanContent = $CleanContent -replace '\r?\n\r?\n\r?\n+', "`r`n`r`n"
            
            # Adicionar apenas um IMPLEMENT_MODULE no final do arquivo
            $CleanContent = $CleanContent.TrimEnd() + "`r`n`r`nIMPLEMENT_MODULE(FDefaultModuleImpl, $ModuleName);"
            
            Set-Content -Path $MainCppFile.FullName -Value $CleanContent -Encoding UTF8
            Write-Host "  ✅ Módulo limpo e corrigido!" -ForegroundColor Green
        }
    }
}

Write-Host "`n🎉 Limpeza concluída!" -ForegroundColor Cyan
