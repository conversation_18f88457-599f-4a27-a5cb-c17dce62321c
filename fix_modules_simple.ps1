# Script simples para limpar IMPLEMENT_MODULE duplicados
$ModulesWithProblems = @(
    "AuracronAnalyticsBridge",
    "AuracronMasterOrchestrator", 
    "AuracronRealmsBridge"
)

foreach ($ModuleName in $ModulesWithProblems) {
    Write-Host "Limpando modulo: $ModuleName"
    
    $ModulePath = "Source\$ModuleName"
    if (Test-Path $ModulePath) {
        $CppFiles = Get-ChildItem -Path $ModulePath -Recurse -Filter "*.cpp"
        $MainCppFile = $null
        
        foreach ($file in $CppFiles) {
            if ($file.Name -like "*$ModuleName*.cpp") {
                $MainCppFile = $file
                break
            }
        }
        
        if (-not $MainCppFile -and $CppFiles.Count -gt 0) {
            $MainCppFile = $CppFiles[0]
        }
        
        if ($MainCppFile) {
            Write-Host "  Processando: $($MainCppFile.FullName)"
            
            $Content = Get-Content $MainCppFile.FullName -Raw
            
            # Remover todas as linhas IMPLEMENT_MODULE
            $CleanContent = $Content -replace 'IMPLEMENT_MODULE\(FDefaultModuleImpl, [^)]+\);\r?\n?', ''
            
            # Remover includes duplicados
            $CleanContent = $CleanContent -replace '#include "Modules/ModuleManager\.h"\r?\n?', ''
            
            # Remover linhas vazias extras
            $CleanContent = $CleanContent -replace '\r?\n\r?\n\r?\n+', "`r`n`r`n"
            
            # Adicionar apenas um IMPLEMENT_MODULE no final
            $FinalContent = $CleanContent.TrimEnd() + "`r`n`r`n#include `"Modules/ModuleManager.h`"`r`n`r`nIMPLEMENT_MODULE(FDefaultModuleImpl, $ModuleName);"
            
            Set-Content -Path $MainCppFile.FullName -Value $FinalContent -Encoding UTF8
            Write-Host "  Concluido: $ModuleName"
        }
    }
}

Write-Host "Limpeza concluida!"
