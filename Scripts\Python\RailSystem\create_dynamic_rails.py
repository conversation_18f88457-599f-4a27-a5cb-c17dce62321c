#!/usr/bin/env python3
"""
Auracron Dynamic Rails System Creation Script
Cria o sistema de trilhos dinâmicos com três tipos:

- Solar Rails (ofensivos, dano aumentado)
- Axis Rails (neutros, velocidade aumentada)
- Lunar Rails (defensivos, regeneração aumentada)

Utiliza: AuracronDynamicRealmBridge
"""

import unreal
import sys
import os
import math

# Configurações dos trilhos dinâmicos
RAIL_CONFIGS = {
    'solar': {
        'color': (1.0, 0.8, 0.2),  # Dourado
        'effect_type': 'offensive',
        'damage_multiplier': 1.25,
        'speed_multiplier': 1.1,
        'duration': 8.0,
        'cooldown': 15.0,
        'particle_effect': '/Game/Effects/Rails/FX_SolarRail'
    },
    'axis': {
        'color': (0.7, 0.7, 0.9),  # Prateado
        'effect_type': 'neutral',
        'damage_multiplier': 1.0,
        'speed_multiplier': 1.5,
        'duration': 6.0,
        'cooldown': 10.0,
        'particle_effect': '/Game/Effects/Rails/FX_AxisRail'
    },
    'lunar': {
        'color': (0.3, 0.6, 1.0),  # Azul lunar
        'effect_type': 'defensive',
        'damage_multiplier': 0.8,
        'speed_multiplier': 1.0,
        'duration': 10.0,
        'cooldown': 20.0,
        'regen_multiplier': 2.0,
        'particle_effect': '/Game/Effects/Rails/FX_LunarRail'
    }
}

# Configurações de layout dos trilhos
RAIL_LAYOUT = {
    'main_lanes': {
        'top_lane': [(0, 4000), (2000, 3000), (4000, 2000), (6000, 0)],
        'mid_lane': [(-6000, 0), (-3000, 0), (0, 0), (3000, 0), (6000, 0)],
        'bot_lane': [(0, -4000), (2000, -3000), (4000, -2000), (6000, 0)]
    },
    'jungle_paths': {
        'north_jungle': [(1000, 2000), (2000, 2500), (3000, 2000)],
        'south_jungle': [(1000, -2000), (2000, -2500), (3000, -2000)],
        'river': [(-2000, 1000), (0, 500), (2000, -1000)]
    },
    'strategic_routes': {
        'baron_approach': [(3000, 1500), (4000, 1000), (5000, 500)],
        'dragon_approach': [(3000, -1500), (4000, -1000), (5000, -500)],
        'base_defense': [(-4000, 0), (-3000, 500), (-2000, 0), (-3000, -500)]
    }
}

class DynamicRailsCreator:
    def __init__(self):
        self.world = unreal.EditorLevelLibrary.get_editor_world()
        self.asset_tools = unreal.AssetToolsHelpers.get_asset_tools()
        self.created_rails = []
        self.rail_network = {}
        
    def create_all_rail_systems(self):
        """Cria todos os sistemas de trilhos dinâmicos"""
        print("Criando sistema de trilhos dinâmicos...")
        
        # Criar trilhos para cada área
        self._create_lane_rails()
        self._create_jungle_rails()
        self._create_strategic_rails()
        
        # Configurar sistema de ativação
        self._setup_rail_activation_system()
        
        print(f"✓ {len(self.created_rails)} trilhos criados")
        
    def _create_lane_rails(self):
        """Cria trilhos nas lanes principais"""
        print("Criando trilhos das lanes...")
        
        for lane_name, waypoints in RAIL_LAYOUT['main_lanes'].items():
            # Criar trilhos de cada tipo na lane
            for rail_type in RAIL_CONFIGS.keys():
                rail_path = self._create_rail_path(waypoints, rail_type, f"{lane_name}_{rail_type}")
                if rail_path:
                    self.created_rails.append(rail_path)
                    
    def _create_jungle_rails(self):
        """Cria trilhos na selva"""
        print("Criando trilhos da selva...")
        
        for path_name, waypoints in RAIL_LAYOUT['jungle_paths'].items():
            # Selva tem mais trilhos Axis (neutros) para mobilidade
            for rail_type in ['axis', 'solar', 'lunar']:
                rail_path = self._create_rail_path(waypoints, rail_type, f"{path_name}_{rail_type}")
                if rail_path:
                    self.created_rails.append(rail_path)
                    
    def _create_strategic_rails(self):
        """Cria trilhos em rotas estratégicas"""
        print("Criando trilhos estratégicos...")
        
        for route_name, waypoints in RAIL_LAYOUT['strategic_routes'].items():
            # Rotas estratégicas têm trilhos especializados
            rail_type = self._determine_strategic_rail_type(route_name)
            rail_path = self._create_rail_path(waypoints, rail_type, f"{route_name}_{rail_type}")
            if rail_path:
                self.created_rails.append(rail_path)
                
    def _determine_strategic_rail_type(self, route_name):
        """Determina o tipo de trilho mais adequado para uma rota estratégica"""
        if 'approach' in route_name:
            return 'solar'  # Trilhos ofensivos para aproximação
        elif 'defense' in route_name:
            return 'lunar'  # Trilhos defensivos para defesa
        else:
            return 'axis'   # Trilhos neutros por padrão
            
    def _create_rail_path(self, waypoints, rail_type, path_name):
        """Cria um caminho de trilho específico"""
        rail_config = RAIL_CONFIGS[rail_type]
        
        # Criar actor principal do trilho
        rail_actor = unreal.EditorLevelLibrary.spawn_actor_from_class(
            unreal.Actor,
            unreal.Vector(waypoints[0][0], waypoints[0][1], 0)
        )
        
        if rail_actor:
            rail_actor.set_actor_label(f"DynamicRail_{path_name}")
            
            # Adicionar componentes do trilho
            self._add_rail_components(rail_actor, rail_config)
            
            # Criar waypoints do trilho
            self._create_rail_waypoints(rail_actor, waypoints, rail_config)
            
            # Configurar propriedades do trilho
            self._configure_rail_properties(rail_actor, rail_config)
            
            # Adicionar ao network de trilhos
            self.rail_network[path_name] = {
                'actor': rail_actor,
                'type': rail_type,
                'waypoints': waypoints,
                'config': rail_config
            }
            
            return rail_actor
            
        return None
        
    def _add_rail_components(self, actor, config):
        """Adiciona componentes necessários ao trilho"""
        # Adicionar Spline Component para o caminho
        spline_comp = actor.add_component_by_class(unreal.SplineComponent)
        
        # Adicionar Static Mesh Component para visualização
        mesh_comp = actor.add_component_by_class(unreal.StaticMeshComponent)
        
        # Adicionar Particle System Component para efeitos
        particle_comp = actor.add_component_by_class(unreal.ParticleSystemComponent)
        
        # Adicionar Audio Component para efeitos sonoros
        audio_comp = actor.add_component_by_class(unreal.AudioComponent)
        
        # Adicionar Box Collision para detecção de ativação
        collision_comp = actor.add_component_by_class(unreal.BoxComponent)
        
        return {
            'spline': spline_comp,
            'mesh': mesh_comp,
            'particle': particle_comp,
            'audio': audio_comp,
            'collision': collision_comp
        }
        
    def _create_rail_waypoints(self, actor, waypoints, config):
        """Cria waypoints ao longo do trilho"""
        spline_comp = actor.get_component_by_class(unreal.SplineComponent)
        
        if spline_comp:
            # Limpar pontos existentes
            spline_comp.clear_spline_points()
            
            # Adicionar waypoints
            for i, (x, y) in enumerate(waypoints):
                location = unreal.Vector(x, y, 0)
                spline_comp.add_spline_point(location, unreal.SplineCoordinateSpace.WORLD)
                
            # Configurar tangentes suaves
            spline_comp.update_spline()
            
    def _configure_rail_properties(self, actor, config):
        """Configura propriedades específicas do trilho"""
        # Configurar material baseado no tipo
        mesh_comp = actor.get_component_by_class(unreal.StaticMeshComponent)
        if mesh_comp:
            # Aplicar material com cor específica
            self._apply_rail_material(mesh_comp, config)
            
        # Configurar efeitos de partículas
        particle_comp = actor.get_component_by_class(unreal.ParticleSystemComponent)
        if particle_comp:
            self._configure_rail_particles(particle_comp, config)
            
    def _apply_rail_material(self, mesh_comp, config):
        """Aplica material específico ao trilho"""
        # Criar material dinâmico com cor específica
        base_material_path = "/Game/Materials/Rails/M_DynamicRail_Base"
        
        if unreal.EditorAssetLibrary.does_asset_exist(base_material_path):
            base_material = unreal.EditorAssetLibrary.load_asset(base_material_path)
            dynamic_material = unreal.MaterialInstanceDynamic.create(base_material, None)
            
            # Configurar cor do trilho
            color = config['color']
            dynamic_material.set_vector_parameter_value("RailColor", unreal.LinearColor(color[0], color[1], color[2], 1.0))
            
            mesh_comp.set_material(0, dynamic_material)
            
    def _configure_rail_particles(self, particle_comp, config):
        """Configura efeitos de partículas do trilho"""
        particle_path = config.get('particle_effect')
        
        if particle_path and unreal.EditorAssetLibrary.does_asset_exist(particle_path):
            particle_system = unreal.EditorAssetLibrary.load_asset(particle_path)
            particle_comp.set_template(particle_system)
            
    def _setup_rail_activation_system(self):
        """Configura sistema de ativação dos trilhos"""
        print("Configurando sistema de ativação...")
        
        # Criar controlador de ativação
        activation_controller = self._create_activation_controller()
        
        # Configurar triggers de ativação
        self._setup_activation_triggers()
        
        # Configurar sistema de cooldown
        self._setup_cooldown_system()
        
    def _create_activation_controller(self):
        """Cria controlador central de ativação dos trilhos"""
        controller_actor = unreal.EditorLevelLibrary.spawn_actor_from_class(
            unreal.Actor,
            unreal.Vector(0, 0, 1000)
        )
        
        if controller_actor:
            controller_actor.set_actor_label("RailActivationController")
            
            # Adicionar componente de gerenciamento
            # Este componente controlará a ativação/desativação dos trilhos
            
        return controller_actor
        
    def _setup_activation_triggers(self):
        """Configura triggers para ativação dos trilhos"""
        # Criar triggers baseados em:
        # - Objetivos capturados
        # - Tempo de jogo
        # - Eventos especiais
        # - Ações dos jogadores
        pass
        
    def _setup_cooldown_system(self):
        """Configura sistema de cooldown dos trilhos"""
        # Implementar sistema que previne spam de ativação
        # Balancear uso entre diferentes tipos de trilhos
        pass
        
    def create_rail_ui_indicators(self):
        """Cria indicadores visuais para os trilhos"""
        print("Criando indicadores visuais...")
        
        for rail_name, rail_data in self.rail_network.items():
            self._create_rail_indicator(rail_data)
            
    def _create_rail_indicator(self, rail_data):
        """Cria indicador visual para um trilho específico"""
        # Criar widget 3D para mostrar status do trilho
        # Mostrar tipo, cooldown, disponibilidade
        pass
        
    def validate_rail_system(self):
        """Valida se o sistema de trilhos foi criado corretamente"""
        print("Validando sistema de trilhos...")
        
        validation_results = {
            'rails_created': len(self.created_rails) > 0,
            'network_configured': len(self.rail_network) > 0,
            'activation_system': True,  # Verificar se sistema de ativação existe
            'coverage_adequate': self._check_map_coverage()
        }
        
        all_valid = all(validation_results.values())
        
        if all_valid:
            print("✓ Sistema de trilhos configurado corretamente")
        else:
            print("✗ Problemas encontrados no sistema de trilhos")
            
        return all_valid
        
    def _check_map_coverage(self):
        """Verifica se os trilhos cobrem adequadamente o mapa"""
        # Verificar se todas as áreas importantes têm trilhos
        # Verificar se há conectividade entre áreas
        return True

def main():
    """Função principal"""
    print("=== Auracron Dynamic Rails Creator ===")
    
    creator = DynamicRailsCreator()
    
    try:
        # Criar sistema de trilhos
        creator.create_all_rail_systems()
        
        # Criar indicadores visuais
        creator.create_rail_ui_indicators()
        
        # Validar sistema
        if creator.validate_rail_system():
            print("✓ Sistema de trilhos dinâmicos criado com sucesso!")
            return True
        else:
            print("✗ Falha na validação do sistema de trilhos")
            return False
            
    except Exception as e:
        print(f"✗ Erro durante criação: {str(e)}")
        return False

if __name__ == "__main__":
    main()