# Script para limpar TODOS os módulos com IMPLEMENT_MODULE duplicados
Write-Host "🧹 Limpando TODOS os módulos com IMPLEMENT_MODULE duplicados..." -ForegroundColor Cyan

Get-ChildItem -Path "Source" -Directory | ForEach-Object {
    $ModuleName = $_.Name
    $ModulePath = $_.FullName
    
    # Buscar arquivo .cpp principal
    $CppFiles = Get-ChildItem -Path $ModulePath -Recurse -Filter "*.cpp"
    $MainCppFile = $null
    
    # Tentar encontrar arquivo principal do módulo
    foreach ($file in $CppFiles) {
        if ($file.Name -like "*$ModuleName*.cpp" -or $file.Name -like "*Module*.cpp") {
            $MainCppFile = $file
            break
        }
    }
    
    # Se não encontrou, usar o primeiro arquivo .cpp
    if (-not $MainCppFile -and $CppFiles.Count -gt 0) {
        $MainCppFile = $CppFiles[0]
    }
    
    if ($MainCppFile) {
        $Content = Get-Content $MainCppFile.FullName -Raw
        
        # Verificar se tem IMPLEMENT_MODULE duplicado
        $ImplementCount = ([regex]::Matches($Content, "IMPLEMENT_MODULE")).Count
        
        if ($ImplementCount -gt 1) {
            Write-Host "  🔧 Limpando: $ModuleName ($ImplementCount duplicatas)" -ForegroundColor Yellow
            
            # Remover todas as linhas IMPLEMENT_MODULE e includes relacionados
            $CleanContent = $Content -replace '#include\s+"Modules/ModuleManager\.h"\s*\r?\n?', ''
            $CleanContent = $CleanContent -replace 'IMPLEMENT_MODULE\(FDefaultModuleImpl,\s*[^)]+\);\s*\r?\n?', ''
            
            # Remover linhas vazias extras
            $CleanContent = $CleanContent -replace '\r?\n\r?\n\r?\n+', "`r`n`r`n"
            
            # Adicionar apenas um IMPLEMENT_MODULE no final do arquivo
            $CleanContent = $CleanContent.TrimEnd() + "`r`n`r`n#include `"Modules/ModuleManager.h`"`r`n`r`nIMPLEMENT_MODULE(FDefaultModuleImpl, $ModuleName);"
            
            Set-Content -Path $MainCppFile.FullName -Value $CleanContent -Encoding UTF8
            Write-Host "    ✅ $ModuleName limpo!" -ForegroundColor Green
        }
        elseif ($ImplementCount -eq 0) {
            Write-Host "  ➕ Adicionando IMPLEMENT_MODULE: $ModuleName" -ForegroundColor Blue
            
            # Adicionar IMPLEMENT_MODULE no final do arquivo
            $CleanContent = $Content.TrimEnd() + "`r`n`r`n#include `"Modules/ModuleManager.h`"`r`n`r`nIMPLEMENT_MODULE(FDefaultModuleImpl, $ModuleName);"
            
            Set-Content -Path $MainCppFile.FullName -Value $CleanContent -Encoding UTF8
            Write-Host "    ✅ $ModuleName corrigido!" -ForegroundColor Green
        }
        else {
            Write-Host "  ✅ $ModuleName já está correto" -ForegroundColor Green
        }
    }
}

Write-Host "`n🎉 Limpeza completa de todos os módulos concluída!" -ForegroundColor Cyan
