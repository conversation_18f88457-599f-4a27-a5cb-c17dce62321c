#!/usr/bin/env python3
"""
Auracron Prismal Flow System Creation Script
Cria o sistema de Fluxo Prismal e Ilhas Estratégicas

Componentes:
- Ilhas Prismáticas flutuantes
- Correntes de energia Prismal
- Pontos de convergência energética
- Sistema de controle territorial
- Mecânicas de captura e defesa

Utiliza: AuracronPCGBridge, AuracronDynamicRealmBridge
"""

import unreal
import sys
import os
import math
import random

# Configurações das Ilhas Prismáticas
PRISMAL_ISLANDS = {
    'major_islands': {
        'count': 3,
        'size_range': (800, 1200),
        'height_range': (500, 800),
        'strategic_value': 'high',
        'capture_time': 30.0,
        'defense_bonus': 2.0,
        'energy_generation': 100
    },
    'minor_islands': {
        'count': 6,
        'size_range': (400, 600),
        'height_range': (300, 500),
        'strategic_value': 'medium',
        'capture_time': 15.0,
        'defense_bonus': 1.5,
        'energy_generation': 50
    },
    'micro_islands': {
        'count': 12,
        'size_range': (200, 300),
        'height_range': (200, 350),
        'strategic_value': 'low',
        'capture_time': 8.0,
        'defense_bonus': 1.2,
        'energy_generation': 25
    }
}

# Configurações do Fluxo Prismal
PRISMAL_FLOW_CONFIG = {
    'energy_streams': {
        'primary_streams': 3,    # Conectam ilhas principais
        'secondary_streams': 6,  # Conectam ilhas menores
        'micro_streams': 12,     # Conectam micro ilhas
        'flow_speed': 500.0,     # Velocidade do fluxo
        'energy_capacity': 1000  # Capacidade energética
    },
    'convergence_points': {
        'major_convergences': 2, # Pontos de alta energia
        'minor_convergences': 4, # Pontos de média energia
        'energy_multiplier': 1.5,
        'area_of_effect': 300.0
    },
    'temporal_cycles': {
        'cycle_duration': 180.0, # 3 minutos por ciclo
        'peak_intensity': 2.0,   # Multiplicador no pico
        'low_intensity': 0.5     # Multiplicador no vale
    }
}

# Posições estratégicas para ilhas
STRATEGIC_POSITIONS = {
    'major_islands': [
        (0, 2500, 600),      # Norte - controla top lane
        (-2000, -1500, 700), # Sudoeste - controla bot lane
        (2000, -1500, 650)   # Sudeste - controla jungle
    ],
    'minor_islands': [
        (1500, 1500, 400),   # Nordeste
        (-1500, 1500, 450),  # Noroeste
        (2500, 0, 350),      # Leste
        (-2500, 0, 380),     # Oeste
        (0, -2000, 420),     # Sul
        (0, 0, 500)          # Centro
    ]
}

class PrismalFlowCreator:
    def __init__(self):
        self.world = unreal.EditorLevelLibrary.get_editor_world()
        self.asset_tools = unreal.AssetToolsHelpers.get_asset_tools()
        self.created_islands = []
        self.energy_streams = []
        self.convergence_points = []
        
    def create_prismal_flow_system(self):
        """Cria o sistema completo de Fluxo Prismal"""
        print("Criando sistema de Fluxo Prismal...")
        
        # Criar ilhas prismáticas
        self._create_prismal_islands()
        
        # Criar correntes de energia
        self._create_energy_streams()
        
        # Criar pontos de convergência
        self._create_convergence_points()
        
        # Configurar sistema temporal
        self._setup_temporal_cycles()
        
        # Configurar mecânicas de captura
        self._setup_capture_mechanics()
        
        print(f"✓ Sistema criado: {len(self.created_islands)} ilhas, {len(self.energy_streams)} correntes")
        
    def _create_prismal_islands(self):
        """Cria todas as ilhas prismáticas"""
        print("Criando ilhas prismáticas...")
        
        # Criar ilhas principais
        self._create_island_tier('major_islands')
        
        # Criar ilhas menores
        self._create_island_tier('minor_islands')
        
        # Criar micro ilhas
        self._create_island_tier('micro_islands')
        
    def _create_island_tier(self, tier_name):
        """Cria um nível específico de ilhas"""
        config = PRISMAL_ISLANDS[tier_name]
        
        if tier_name in STRATEGIC_POSITIONS:
            positions = STRATEGIC_POSITIONS[tier_name]
        else:
            positions = self._generate_random_positions(config['count'])
            
        for i, position in enumerate(positions[:config['count']]):
            island = self._create_single_island(tier_name, position, i, config)
            if island:
                self.created_islands.append(island)
                
    def _generate_random_positions(self, count):
        """Gera posições aleatórias para ilhas"""
        positions = []
        for i in range(count):
            # Gerar posições em círculos concêntricos
            radius = 1000 + (i * 300)
            angle = (2 * math.pi * i) / count
            x = radius * math.cos(angle)
            y = radius * math.sin(angle)
            z = random.randint(200, 600)
            positions.append((x, y, z))
        return positions
        
    def _create_single_island(self, tier_name, position, index, config):
        """Cria uma ilha individual"""
        island_name = f"PrismalIsland_{tier_name}_{index}"
        
        # Criar actor da ilha
        island_actor = unreal.EditorLevelLibrary.spawn_actor_from_class(
            unreal.Actor,
            unreal.Vector(position[0], position[1], position[2])
        )
        
        if island_actor:
            island_actor.set_actor_label(island_name)
            
            # Adicionar componentes da ilha
            components = self._add_island_components(island_actor, config)
            
            # Configurar propriedades da ilha
            self._configure_island_properties(island_actor, config, components)
            
            # Adicionar sistema de captura
            self._add_capture_system(island_actor, config)
            
            return island_actor
            
        return None
        
    def _add_island_components(self, actor, config):
        """Adiciona componentes necessários à ilha"""
        components = {}
        
        # Componente de mesh principal
        components['main_mesh'] = actor.add_component_by_class(unreal.StaticMeshComponent)
        
        # Componente de cristais energéticos
        components['crystal_mesh'] = actor.add_component_by_class(unreal.StaticMeshComponent)
        
        # Componente de efeitos de energia
        components['energy_effect'] = actor.add_component_by_class(unreal.NiagaraComponent)
        
        # Componente de áudio ambiente
        components['ambient_audio'] = actor.add_component_by_class(unreal.AudioComponent)
        
        # Componente de colisão para captura
        components['capture_collision'] = actor.add_component_by_class(unreal.SphereComponent)
        
        # Componente de área de influência
        components['influence_area'] = actor.add_component_by_class(unreal.SphereComponent)
        
        return components
        
    def _configure_island_properties(self, actor, config, components):
        """Configura propriedades específicas da ilha"""
        # Configurar tamanho baseado na configuração
        size = random.uniform(config['size_range'][0], config['size_range'][1])
        scale = unreal.Vector(size/100, size/100, size/100)
        
        # Aplicar escala aos componentes de mesh
        if components['main_mesh']:
            components['main_mesh'].set_world_scale3d(scale)
            
        # Configurar área de captura
        if components['capture_collision']:
            components['capture_collision'].set_sphere_radius(size * 0.8)
            
        # Configurar área de influência
        if components['influence_area']:
            components['influence_area'].set_sphere_radius(size * 1.5)
            
        # Configurar efeitos visuais
        self._configure_island_effects(components, config)
        
    def _configure_island_effects(self, components, config):
        """Configura efeitos visuais da ilha"""
        # Configurar efeito de energia baseado no valor estratégico
        if components['energy_effect']:
            effect_intensity = {
                'high': 1.0,
                'medium': 0.7,
                'low': 0.4
            }.get(config['strategic_value'], 0.5)
            
            # Aplicar intensidade do efeito
            # components['energy_effect'].set_float_parameter("Intensity", effect_intensity)
            
    def _add_capture_system(self, actor, config):
        """Adiciona sistema de captura à ilha"""
        # Adicionar componente de captura customizado
        # Este componente gerenciará:
        # - Tempo de captura
        # - Progresso de captura
        # - Estado de controle (neutro/team1/team2)
        # - Bônus defensivos
        pass
        
    def _create_energy_streams(self):
        """Cria correntes de energia entre ilhas"""
        print("Criando correntes de energia...")
        
        # Criar streams primários (conectam ilhas principais)
        self._create_primary_streams()
        
        # Criar streams secundários (conectam ilhas menores)
        self._create_secondary_streams()
        
        # Criar micro streams (conectam micro ilhas)
        self._create_micro_streams()
        
    def _create_primary_streams(self):
        """Cria correntes primárias de energia"""
        major_islands = [island for island in self.created_islands 
                        if 'major' in island.get_actor_label()]
        
        # Conectar ilhas principais em triângulo
        for i in range(len(major_islands)):
            for j in range(i + 1, len(major_islands)):
                stream = self._create_energy_stream(
                    major_islands[i], 
                    major_islands[j], 
                    'primary'
                )
                if stream:
                    self.energy_streams.append(stream)
                    
    def _create_secondary_streams(self):
        """Cria correntes secundárias de energia"""
        minor_islands = [island for island in self.created_islands 
                        if 'minor' in island.get_actor_label()]
        major_islands = [island for island in self.created_islands 
                        if 'major' in island.get_actor_label()]
        
        # Conectar cada ilha menor à ilha principal mais próxima
        for minor_island in minor_islands:
            closest_major = self._find_closest_island(minor_island, major_islands)
            if closest_major:
                stream = self._create_energy_stream(
                    minor_island, 
                    closest_major, 
                    'secondary'
                )
                if stream:
                    self.energy_streams.append(stream)
                    
    def _create_micro_streams(self):
        """Cria micro correntes de energia"""
        micro_islands = [island for island in self.created_islands 
                        if 'micro' in island.get_actor_label()]
        other_islands = [island for island in self.created_islands 
                        if 'micro' not in island.get_actor_label()]
        
        # Conectar cada micro ilha à ilha mais próxima
        for micro_island in micro_islands:
            closest_island = self._find_closest_island(micro_island, other_islands)
            if closest_island:
                stream = self._create_energy_stream(
                    micro_island, 
                    closest_island, 
                    'micro'
                )
                if stream:
                    self.energy_streams.append(stream)
                    
    def _find_closest_island(self, source_island, target_islands):
        """Encontra a ilha mais próxima"""
        if not target_islands:
            return None
            
        source_location = source_island.get_actor_location()
        closest_island = None
        min_distance = float('inf')
        
        for target_island in target_islands:
            target_location = target_island.get_actor_location()
            distance = unreal.Vector.dist(source_location, target_location)
            
            if distance < min_distance:
                min_distance = distance
                closest_island = target_island
                
        return closest_island
        
    def _create_energy_stream(self, island_a, island_b, stream_type):
        """Cria uma corrente de energia entre duas ilhas"""
        stream_name = f"EnergyStream_{stream_type}_{island_a.get_actor_label()}_{island_b.get_actor_label()}"
        
        # Criar actor da corrente
        stream_actor = unreal.EditorLevelLibrary.spawn_actor_from_class(
            unreal.Actor,
            island_a.get_actor_location()
        )
        
        if stream_actor:
            stream_actor.set_actor_label(stream_name)
            
            # Adicionar componentes da corrente
            self._add_stream_components(stream_actor, island_a, island_b, stream_type)
            
            return stream_actor
            
        return None
        
    def _add_stream_components(self, actor, island_a, island_b, stream_type):
        """Adiciona componentes à corrente de energia"""
        # Adicionar Spline Component para o caminho
        spline_comp = actor.add_component_by_class(unreal.SplineComponent)
        
        # Configurar pontos do spline
        if spline_comp:
            spline_comp.clear_spline_points()
            spline_comp.add_spline_point(island_a.get_actor_location(), unreal.SplineCoordinateSpace.WORLD)
            spline_comp.add_spline_point(island_b.get_actor_location(), unreal.SplineCoordinateSpace.WORLD)
            spline_comp.update_spline()
            
        # Adicionar efeito visual da corrente
        niagara_comp = actor.add_component_by_class(unreal.NiagaraComponent)
        
        # Adicionar áudio da corrente
        audio_comp = actor.add_component_by_class(unreal.AudioComponent)
        
    def _create_convergence_points(self):
        """Cria pontos de convergência energética"""
        print("Criando pontos de convergência...")
        
        # Criar convergências principais
        self._create_major_convergences()
        
        # Criar convergências menores
        self._create_minor_convergences()
        
    def _create_major_convergences(self):
        """Cria pontos de convergência principais"""
        # Posições estratégicas para convergências principais
        major_positions = [
            (0, 1000, 400),   # Norte do centro
            (0, -1000, 350)   # Sul do centro
        ]
        
        for i, position in enumerate(major_positions):
            convergence = self._create_convergence_point(position, 'major', i)
            if convergence:
                self.convergence_points.append(convergence)
                
    def _create_minor_convergences(self):
        """Cria pontos de convergência menores"""
        # Posições para convergências menores
        minor_positions = [
            (1500, 500, 300),
            (-1500, 500, 320),
            (1500, -500, 280),
            (-1500, -500, 310)
        ]
        
        for i, position in enumerate(minor_positions):
            convergence = self._create_convergence_point(position, 'minor', i)
            if convergence:
                self.convergence_points.append(convergence)
                
    def _create_convergence_point(self, position, convergence_type, index):
        """Cria um ponto de convergência individual"""
        convergence_name = f"ConvergencePoint_{convergence_type}_{index}"
        
        # Criar actor do ponto de convergência
        convergence_actor = unreal.EditorLevelLibrary.spawn_actor_from_class(
            unreal.Actor,
            unreal.Vector(position[0], position[1], position[2])
        )
        
        if convergence_actor:
            convergence_actor.set_actor_label(convergence_name)
            
            # Adicionar componentes do ponto de convergência
            self._add_convergence_components(convergence_actor, convergence_type)
            
            return convergence_actor
            
        return None
        
    def _add_convergence_components(self, actor, convergence_type):
        """Adiciona componentes ao ponto de convergência"""
        # Componente visual principal
        mesh_comp = actor.add_component_by_class(unreal.StaticMeshComponent)
        
        # Efeito de energia convergente
        energy_effect = actor.add_component_by_class(unreal.NiagaraComponent)
        
        # Área de efeito
        area_comp = actor.add_component_by_class(unreal.SphereComponent)
        
        # Configurar tamanho baseado no tipo
        if convergence_type == 'major':
            area_comp.set_sphere_radius(400.0)
        else:
            area_comp.set_sphere_radius(250.0)
            
        # Áudio ambiente
        audio_comp = actor.add_component_by_class(unreal.AudioComponent)
        
    def _setup_temporal_cycles(self):
        """Configura ciclos temporais do fluxo prismal"""
        print("Configurando ciclos temporais...")
        
        # Criar controlador de ciclos temporais
        cycle_controller = self._create_cycle_controller()
        
        # Configurar fases do ciclo
        self._configure_cycle_phases()
        
    def _create_cycle_controller(self):
        """Cria controlador de ciclos temporais"""
        controller_actor = unreal.EditorLevelLibrary.spawn_actor_from_class(
            unreal.Actor,
            unreal.Vector(0, 0, 2000)
        )
        
        if controller_actor:
            controller_actor.set_actor_label("PrismalCycleController")
            
            # Adicionar componente de gerenciamento temporal
            # Este componente controlará os ciclos de energia
            
        return controller_actor
        
    def _configure_cycle_phases(self):
        """Configura as fases dos ciclos temporais"""
        # Fase de crescimento (energia aumentando)
        # Fase de pico (energia máxima)
        # Fase de declínio (energia diminuindo)
        # Fase de vale (energia mínima)
        pass
        
    def _setup_capture_mechanics(self):
        """Configura mecânicas de captura das ilhas"""
        print("Configurando mecânicas de captura...")
        
        # Configurar sistema de progresso de captura
        self._setup_capture_progress_system()
        
        # Configurar bônus de controle
        self._setup_control_bonuses()
        
        # Configurar sistema de defesa
        self._setup_defense_system()
        
    def _setup_capture_progress_system(self):
        """Configura sistema de progresso de captura"""
        # Sistema de captura baseado em presença de jogadores
        # Velocidade de captura baseada no número de jogadores
        # Interrupção de captura por inimigos
        pass
        
    def _setup_control_bonuses(self):
        """Configura bônus por controlar ilhas"""
        # Bônus de geração de energia
        # Bônus de experiência
        # Bônus de ouro
        # Bônus de visão
        pass
        
    def _setup_defense_system(self):
        """Configura sistema de defesa das ilhas"""
        # Torres defensivas automáticas
        # Escudos energéticos
        # Armadilhas ativadas
        pass
        
    def validate_prismal_system(self):
        """Valida se o sistema foi criado corretamente"""
        print("Validando sistema de Fluxo Prismal...")
        
        validation_results = {
            'islands_created': len(self.created_islands) > 0,
            'streams_created': len(self.energy_streams) > 0,
            'convergences_created': len(self.convergence_points) > 0,
            'temporal_system': True,  # Verificar se sistema temporal existe
            'capture_mechanics': True # Verificar se mecânicas de captura existem
        }
        
        all_valid = all(validation_results.values())
        
        if all_valid:
            print("✓ Sistema de Fluxo Prismal configurado corretamente")
            print(f"  - {len(self.created_islands)} ilhas criadas")
            print(f"  - {len(self.energy_streams)} correntes de energia")
            print(f"  - {len(self.convergence_points)} pontos de convergência")
        else:
            print("✗ Problemas encontrados no sistema")
            
        return all_valid

def main():
    """Função principal"""
    print("=== Auracron Prismal Flow System Creator ===")
    
    creator = PrismalFlowCreator()
    
    try:
        # Criar sistema completo
        creator.create_prismal_flow_system()
        
        # Validar criação
        if creator.validate_prismal_system():
            print("✓ Sistema de Fluxo Prismal criado com sucesso!")
            return True
        else:
            print("✗ Falha na validação do sistema")
            return False
            
    except Exception as e:
        print(f"✗ Erro durante criação: {str(e)}")
        return False

if __name__ == "__main__":
    main()