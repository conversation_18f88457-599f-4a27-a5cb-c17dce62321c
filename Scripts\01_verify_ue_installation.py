#!/usr/bin/env python3
"""
Script: 01_verify_ue_installation.py
Descrição: Verificar instalação do Unreal Engine 5.6 e Python Plugin
Bridge: Nenhum (verificação de sistema)
Autor: Auracron Development Team
Data: Janeiro 2025
"""

import sys
import os

def verify_ue_installation():
    """
    Verifica se o Unreal Engine 5.6 está instalado corretamente
    e se o Python Plugin está habilitado.
    """
    print("🔍 Verificando instalação do Unreal Engine 5.6...")
    
    try:
        # Importar módulo unreal (só funciona se estiver no UE)
        import unreal
        print("✅ Módulo 'unreal' importado com sucesso")
        
        # Verificar versão do engine
        try:
            engine_version = unreal.SystemLibrary.get_engine_version()
            print(f"✅ Versão do Unreal Engine: {engine_version}")
            
            # Verificar se é versão 5.6
            if "5.6" in engine_version:
                print("✅ Unreal Engine 5.6 detectado corretamente")
            else:
                print(f"⚠️  Versão detectada ({engine_version}) não é 5.6")
                return False
                
        except Exception as e:
            print(f"❌ Erro ao obter versão do engine: {e}")
            return False
        
        # Verificar se Python Plugin está habilitado
        try:
            # Tentar acessar subsistemas do editor
            editor_subsystem = unreal.get_editor_subsystem(unreal.UnrealEditorSubsystem)
            if editor_subsystem:
                print("✅ Python Editor Script Plugin está habilitado")
            else:
                print("❌ Não foi possível acessar UnrealEditorSubsystem")
                return False
                
        except Exception as e:
            print(f"❌ Erro ao verificar Python Plugin: {e}")
            return False
        
        # Verificar Editor Scripting Utilities Plugin
        try:
            asset_library = unreal.EditorAssetLibrary
            if asset_library:
                print("✅ Editor Scripting Utilities Plugin está disponível")
            else:
                print("⚠️  Editor Scripting Utilities Plugin pode não estar habilitado")
                
        except Exception as e:
            print(f"⚠️  Editor Scripting Utilities não disponível: {e}")
        
        # Verificar versão do Python
        python_version = sys.version
        print(f"✅ Versão do Python: {python_version}")
        
        # Verificar se é Python 3.11.8 (versão padrão do UE 5.6)
        if "3.11" in python_version:
            print("✅ Versão do Python compatível com UE 5.6")
        else:
            print(f"⚠️  Versão do Python ({python_version}) pode não ser a padrão do UE 5.6")
        
        # Verificar paths do Python
        print(f"✅ Python executable: {sys.executable}")
        print(f"✅ Python paths: {len(sys.path)} paths configurados")
        
        # Verificar se estamos no contexto do editor
        try:
            level_editor = unreal.get_editor_subsystem(unreal.LevelEditorSubsystem)
            if level_editor:
                print("✅ Contexto do Level Editor detectado")
                
                # Verificar se há um level carregado
                current_level = level_editor.get_current_level()
                if current_level:
                    level_name = current_level.get_name()
                    print(f"✅ Level atual: {level_name}")
                else:
                    print("⚠️  Nenhum level carregado atualmente")
                    
        except Exception as e:
            print(f"⚠️  Erro ao verificar Level Editor: {e}")
        
        print("\n🎉 Verificação da instalação concluída com sucesso!")
        print("📋 Resumo:")
        print("   - Unreal Engine 5.6: ✅")
        print("   - Python Plugin: ✅") 
        print("   - Editor Scripting: ✅")
        print("   - Ambiente Python: ✅")
        
        return True
        
    except ImportError as e:
        print(f"❌ Erro crítico: Não foi possível importar o módulo 'unreal'")
        print(f"   Detalhes: {e}")
        print("   Certifique-se de que:")
        print("   1. O script está sendo executado dentro do Unreal Editor")
        print("   2. O Python Editor Script Plugin está habilitado")
        print("   3. O projeto está aberto no Unreal Editor")
        return False
        
    except Exception as e:
        print(f"❌ Erro inesperado durante a verificação: {e}")
        return False

def verify_project_structure():
    """
    Verifica se a estrutura do projeto Auracron está correta.
    """
    print("\n🔍 Verificando estrutura do projeto Auracron...")
    
    try:
        # Verificar se estamos no diretório correto do projeto
        current_dir = os.getcwd()
        print(f"📁 Diretório atual: {current_dir}")
        
        # Verificar se existe o diretório Source
        source_dir = os.path.join(current_dir, "Source")
        if os.path.exists(source_dir):
            print("✅ Diretório 'Source' encontrado")
            
            # Listar bridges disponíveis
            bridges = []
            for item in os.listdir(source_dir):
                item_path = os.path.join(source_dir, item)
                if os.path.isdir(item_path) and "Auracron" in item:
                    bridges.append(item)
            
            if bridges:
                print(f"✅ {len(bridges)} bridges Auracron encontrados:")
                for bridge in sorted(bridges):
                    print(f"   - {bridge}")
            else:
                print("⚠️  Nenhum bridge Auracron encontrado no diretório Source")
                
        else:
            print("❌ Diretório 'Source' não encontrado")
            print("   Certifique-se de estar no diretório raiz do projeto Auracron")
            return False
        
        # Verificar arquivo .uproject
        uproject_files = [f for f in os.listdir(current_dir) if f.endswith('.uproject')]
        if uproject_files:
            print(f"✅ Arquivo de projeto encontrado: {uproject_files[0]}")
        else:
            print("⚠️  Arquivo .uproject não encontrado")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro ao verificar estrutura do projeto: {e}")
        return False

def main():
    """
    Função principal que executa todas as verificações.
    """
    print("🌟 AURACRON - Verificação de Instalação UE 5.6")
    print("=" * 50)
    
    # Verificar instalação do UE
    ue_ok = verify_ue_installation()
    
    # Verificar estrutura do projeto
    project_ok = verify_project_structure()
    
    print("\n" + "=" * 50)
    if ue_ok and project_ok:
        print("🎉 SUCESSO: Ambiente está configurado corretamente!")
        print("   Próximo passo: Configurar ambiente Python")
    else:
        print("❌ FALHA: Problemas detectados na configuração")
        print("   Corrija os problemas antes de continuar")
    
    return ue_ok and project_ok

if __name__ == "__main__":
    # Este script deve ser executado dentro do Unreal Editor
    # Via console Python ou como script Python
    main()
